{"version": "2.2", "metadata": {"rules_version": "1.14.2"}, "rules": [{"id": "blk-001-001", "name": "Block IP Addresses", "tags": {"type": "block_ip", "category": "security_response", "module": "network-acl"}, "conditions": [{"parameters": {"inputs": [{"address": "http.client_ip"}], "data": "blocked_ips"}, "operator": "ip_match"}], "transformers": [], "on_match": ["block"]}, {"id": "blk-001-002", "name": "Block User Addresses", "tags": {"type": "block_user", "category": "security_response", "module": "authentication-acl"}, "conditions": [{"parameters": {"inputs": [{"address": "usr.id"}], "data": "blocked_users"}, "operator": "exact_match"}], "transformers": [], "on_match": ["block"]}, {"id": "crs-913-110", "name": "Acunetix", "tags": {"type": "commercial_scanner", "crs_id": "913110", "category": "attack_attempt", "tool_name": "Acunetix", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies"}], "list": ["acunetix-product", "(acunetix web vulnerability scanner", "acunetix-scanning-agreement", "acunetix-user-agreement", "md5(acunetix_wvs_security_test)"]}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-913-120", "name": "Known security scanner filename/argument", "tags": {"type": "security_scanner", "crs_id": "913120", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "list": ["/.adsensepostnottherenonobook", "/<invalid>hello.html", "/actsensepostnottherenonotive", "/acunetix-wvs-test-for-some-inexistent-file", "/antidisestablishmentarianism", "/appscan_fingerprint/mac_address", "/arachni-", "/cybercop", "/nessus_is_probing_you_", "/nessustest", "/netsparker-", "/rfiinc.txt", "/thereisnowaythat-you-canbethere", "/w3af/remotefileinclude.html", "appscan_fingerprint", "w00tw00t.at.isc.sans.dfind", "w00tw00t.at.blackhats.romanian.anti-sec"], "options": {"enforce_word_boundary": true}}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-920-260", "name": "Unicode Full/Half Width Abuse Attack Attempt", "tags": {"type": "http_protocol_violation", "crs_id": "920260", "category": "attack_attempt", "cwe": "176", "capec": "1000/255/153/267/71", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "\\%u[fF]{2}[0-9a-fA-F]{2}", "options": {"case_sensitive": true, "min_length": 6}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-921-110", "name": "HTTP Request Smuggling Attack", "tags": {"type": "http_protocol_violation", "crs_id": "921110", "category": "attack_attempt", "cwe": "444", "capec": "1000/210/272/220/33", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}], "regex": "(?:get|post|head|options|connect|put|delete|trace|track|patch|propfind|propatch|mkcol|copy|move|lock|unlock)\\s+[^\\s]+\\s+http/\\d", "options": {"case_sensitive": true, "min_length": 12}}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "crs-921-160", "name": "HTTP Header Injection Attack via payload (CR/LF and header-name detected)", "tags": {"type": "http_protocol_violation", "crs_id": "921160", "category": "attack_attempt", "cwe": "113", "capec": "1000/210/272/220/105", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.path_params"}], "regex": "[\\n\\r]+(?:refresh|(?:set-)?cookie|(?:x-)?(?:forwarded-(?:for|host|server)|via|remote-ip|remote-addr|originating-IP))\\s*:", "options": {"case_sensitive": true, "min_length": 3}}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "crs-930-100", "name": "Obfuscated Path Traversal Attack (/../)", "tags": {"type": "lfi", "crs_id": "930100", "category": "attack_attempt", "cwe": "22", "capec": "1000/255/153/126", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.uri.raw"}, {"address": "server.request.headers.no_cookies"}], "regex": "(?:%(?:c(?:0%(?:[2aq]f|5c|9v)|1%(?:[19p]c|8s|af))|2(?:5(?:c(?:0%25af|1%259c)|2f|5c)|%46|f)|(?:(?:f(?:8%8)?0%8|e)0%80%a|bg%q)f|%3(?:2(?:%(?:%6|4)6|F)|5%%63)|u(?:221[56]|002f|EFC8|F025)|1u|5c)|0x(?:2f|5c)|\\/|\\x5c)(?:%(?:(?:f(?:(?:c%80|8)%8)?0%8|e)0%80%ae|2(?:(?:5(?:c0%25a|2))?e|%45)|u(?:(?:002|ff0)e|2024)|%32(?:%(?:%6|4)5|E)|c0(?:%[256aef]e|\\.))|\\.(?:%0[01])?|0x2e){2,3}(?:%(?:c(?:0%(?:[2aq]f|5c|9v)|1%(?:[19p]c|8s|af))|2(?:5(?:c(?:0%25af|1%259c)|2f|5c)|%46|f)|(?:(?:f(?:8%8)?0%8|e)0%80%a|bg%q)f|%3(?:2(?:%(?:%6|4)6|F)|5%%63)|u(?:221[56]|002f|EFC8|F025)|1u|5c)|0x(?:2f|5c)|\\/|\\x5c)", "options": {"min_length": 4}}, "operator": "match_regex"}], "transformers": ["normalizePath"]}, {"id": "crs-930-110", "name": "Simple Path Traversal Attack (/../)", "tags": {"type": "lfi", "crs_id": "930110", "category": "attack_attempt", "cwe": "22", "capec": "1000/255/153/126", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.uri.raw"}, {"address": "server.request.headers.no_cookies"}], "regex": "(?:(?:^|[\\x5c/])\\.{2,3}[\\x5c/]|[\\x5c/]\\.{2,3}(?:[\\x5c/]|$))", "options": {"case_sensitive": true, "min_length": 3}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-930-120", "name": "OS File Access Attempt", "tags": {"type": "lfi", "crs_id": "930120", "category": "attack_attempt", "cwe": "22", "capec": "1000/255/153/126", "confidence": "1"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "list": ["/.htaccess", "/.htdigest", "/.htpasswd", "/.addressbook", "/.aptitude/config", ".aws/config", ".aws/credentials", "/.bash_config", "/.bash_history", "/.bash_logout", "/.bash_profile", "/.bashrc", ".cache/notify-osd.log", ".config/odesk/odesk team.conf", "/.cshrc", "/.dockerignore", ".drush/", "/.es<PERSON><PERSON><PERSON>", "/.fbcindex", "/.forward", "/.git", ".git/", "/.gitattributes", "/.gitconfig", ".gnupg/", ".hplip/hplip.conf", "/.ksh_history", "/.lesshst", ".lftp/", "/.lhistory", "/.lldb-history", ".local/share/mc/", "/.lynx_cookies", "/.my.cnf", "/.mysql_history", "/.nano_history", "/.node_repl_history", "/.pear<PERSON>", "/.pgpass", "/.php_history", "/.pinerc", ".pki/", "/.proclog", "/.procmailrc", "/.psql_history", "/.python_history", "/.rediscli_history", "/.rhistory", "/.rhosts", "/.sh_history", "/.sqlite_history", ".ssh/authorized_keys", ".ssh/config", ".ssh/id_dsa", ".ssh/id_dsa.pub", ".ssh/id_rsa", ".ssh/id_rsa.pub", ".ssh/identity", ".ssh/identity.pub", ".ssh/id_ecdsa", ".ssh/id_ecdsa.pub", ".ssh/known_hosts", ".subversion/auth", ".subversion/config", ".subversion/servers", ".tconn/tconn.conf", "/.tcshrc", ".vidalia/vidalia.conf", "/.viminfo", "/.vimrc", "/.www_acl", "/.wwwacl", "/.xauthority", "/.zhistory", "/.zshrc", "/.zsh_history", "/.nsconfig", "data/elasticsearch", "data/kafka", "etc/ansible", "etc/bind", "etc/centos-release", "etc/centos-release-upstream", "etc/clam.d", "etc/elasticsearch", "etc/freshclam.conf", "etc/gshadow", "etc/gshadow-", "etc/httpd", "etc/kafka", "etc/kibana", "etc/logstash", "etc/lvm", "etc/mongod.conf", "etc/my.cnf", "etc/nuxeo.conf", "etc/pki", "etc/postfix", "etc/scw-release", "etc/subgid", "etc/subgid-", "etc/sudoers.d", "etc/sysconfig", "etc/system-release-cpe", "opt/nuxeo", "opt/tomcat", "tmp/kafka-logs", "usr/lib/rpm/rpm.log", "var/data/elasticsearch", "var/lib/elasticsearch", "etc/.java", "etc/acpi", "etc/alsa", "etc/alternatives", "etc/apache2", "etc/apm", "etc/apparmor", "etc/apparmor.d", "etc/apport", "etc/apt", "etc/asciidoc", "etc/avahi", "etc/bash_completion.d", "etc/binfmt.d", "etc/bluetooth", "etc/bonobo-activation", "etc/brltty", "etc/ca-certificates", "etc/calendar", "etc/chatscripts", "etc/chromium-browser", "etc/clamav", "etc/cni", "etc/console-setup", "etc/coraza-waf", "etc/cracklib", "etc/cron.d", "etc/cron.daily", "etc/cron.hourly", "etc/cron.monthly", "etc/cron.weekly", "etc/cups", "etc/cups.save", "etc/cupshelpers", "etc/dbus-1", "etc/dconf", "etc/default", "etc/depmod.d", "etc/dhcp", "etc/dictionaries-common", "etc/dkms", "etc/dnsmasq.d", "etc/dockeretc/dpkg", "etc/emacs", "etc/environment.d", "etc/fail2ban", "etc/firebird", "etc/firefox", "etc/fonts", "etc/fwupd", "etc/gconf", "etc/gdb", "etc/gdm3", "etc/geoclue", "etc/ghostscript", "etc/gimp", "etc/glvnd", "etc/gnome", "etc/gnome-vfs-2.0", "etc/gnucash", "etc/gnustep", "etc/groff", "etc/grub.d", "etc/gss", "etc/gtk-2.0", "etc/gtk-3.0", "etc/hp", "etc/ifplugd", "etc/imagemagick-6", "etc/init", "etc/init.d", "etc/initramfs-tools", "etc/insserv.conf.d", "etc/iproute2", "etc/iptables", "etc/java", "etc/java-11-openjdk", "etc/java-17-oracle", "etc/java-8-openjdk", "etc/kernel", "etc/ld.so.conf.d", "etc/ldap", "etc/libblockdev", "etc/libibverbs.d", "etc/libnl-3", "etc/libpaper.d", "etc/libreoffice", "etc/lighttpd", "etc/logcheck", "etc/logrotate.d", "etc/lynx", "etc/mail", "etc/mc", "etc/menu", "etc/menu-methods", "etc/modprobe.d", "etc/modsecurity", "etc/modules-load.d", "etc/monit", "etc/mono", "etc/mplayer", "etc/mpv", "etc/muttrc.d", "etc/mysql", "etc/netplan", "etc/network", "etc/networkd-dispatcher", "etc/networkmanager", "etc/newt", "etc/nghttpx", "etc/nikto", "etc/odbcdatasources", "etc/openal", "etc/openmpi", "etc/opt", "etc/osync", "etc/packagekit", "etc/pam.d", "etc/pcmcia", "etc/perl", "etc/php", "etc/pki", "etc/pm", "etc/polkit-1", "etc/postfix", "etc/ppp", "etc/profile.d", "etc/proftpd", "etc/pulse", "etc/python", "etc/rc0.d", "etc/rc1.d", "etc/rc2.d", "etc/rc3.d", "etc/rc4.d", "etc/rc5.d", "etc/rc6.d", "etc/rcs.d", "etc/resolvconf", "etc/rsyslog.d", "etc/samba", "etc/sane.d", "etc/security", "etc/selinux", "etc/sensors.d", "etc/sgml", "etc/signon-ui", "etc/skel", "etc/snmp", "etc/sound", "etc/spamassassin", "etc/speech-dispatcher", "etc/ssh", "etc/ssl", "etc/sudoers.d", "etc/sysctl.d", "etc/sysstat", "etc/systemd", "etc/terminfo", "etc/texmf", "etc/thermald", "etc/thnuclnt", "etc/thunderbird", "etc/timidity", "etc/tmpfiles.d", "etc/ubuntu-advantage", "etc/udev", "etc/udisks2", "etc/ufw", "etc/update-manager", "etc/update-motd.d", "etc/update-notifier", "etc/upower", "etc/urlview", "etc/usb_modeswitch.d", "etc/vim", "etc/vmware", "etc/vmware-installer", "etc/vmware-vix", "etc/vulkan", "etc/w3m", "etc/wireshark", "etc/wpa_supplicant", "etc/x11", "etc/xdg", "etc/xml", "etc/redis.conf", "etc/redis-sentinel.conf", "etc/php.ini", "bin/php.ini", "etc/httpd/php.ini", "usr/lib/php.ini", "usr/lib/php/php.ini", "usr/local/etc/php.ini", "usr/local/lib/php.ini", "usr/local/php/lib/php.ini", "usr/local/php4/lib/php.ini", "usr/local/php5/lib/php.ini", "usr/local/apache/conf/php.ini", "etc/php4.4/fcgi/php.ini", "etc/php4/apache/php.ini", "etc/php4/apache2/php.ini", "etc/php5/apache/php.ini", "etc/php5/apache2/php.ini", "etc/php/php.ini", "etc/php/php4/php.ini", "etc/php/apache/php.ini", "etc/php/apache2/php.ini", "web/conf/php.ini", "usr/local/zend/etc/php.ini", "opt/xampp/etc/php.ini", "var/local/www/conf/php.ini", "etc/php/cgi/php.ini", "etc/php4/cgi/php.ini", "etc/php5/cgi/php.ini", "home2/bin/stable/apache/php.ini", "home/bin/stable/apache/php.ini", "etc/httpd/conf.d/php.conf", "php5/php.ini", "php4/php.ini", "php/php.ini", "windows/php.ini", "winnt/php.ini", "apache/php/php.ini", "xampp/apache/bin/php.ini", "netserver/bin/stable/apache/php.ini", "volumes/macintosh_hd1/usr/local/php/lib/php.ini", "etc/mono/1.0/machine.config", "etc/mono/2.0/machine.config", "etc/mono/2.0/web.config", "etc/mono/config", "usr/local/cpanel/logs/stats_log", "usr/local/cpanel/logs/access_log", "usr/local/cpanel/logs/error_log", "usr/local/cpanel/logs/license_log", "usr/local/cpanel/logs/login_log", "var/cpanel/cpanel.config", "usr/local/psa/admin/logs/httpsd_access_log", "usr/local/psa/admin/logs/panel.log", "usr/local/psa/admin/conf/php.ini", "etc/sw-cp-server/applications.d/plesk.conf", "usr/local/psa/admin/conf/site_isolation_settings.ini", "usr/local/sb/config", "etc/sw-cp-server/applications.d/00-sso-cpserver.conf", "etc/sso/sso_config.ini", "etc/mysql/conf.d/old_passwords.cnf", "var/mysql.log", "var/mysql-bin.index", "var/data/mysql-bin.index", "program files/mysql/mysql server 5.0/data/{host}.err", "program files/mysql/mysql server 5.0/data/mysql.log", "program files/mysql/mysql server 5.0/data/mysql.err", "program files/mysql/mysql server 5.0/data/mysql-bin.log", "program files/mysql/mysql server 5.0/data/mysql-bin.index", "program files/mysql/data/{host}.err", "program files/mysql/data/mysql.log", "program files/mysql/data/mysql.err", "program files/mysql/data/mysql-bin.log", "program files/mysql/data/mysql-bin.index", "mysql/data/{host}.err", "mysql/data/mysql.log", "mysql/data/mysql.err", "mysql/data/mysql-bin.log", "mysql/data/mysql-bin.index", "usr/local/mysql/data/mysql.log", "usr/local/mysql/data/mysql.err", "usr/local/mysql/data/mysql-bin.log", "usr/local/mysql/data/mysql-slow.log", "usr/local/mysql/data/mysqlderror.log", "usr/local/mysql/data/{host}.err", "usr/local/mysql/data/mysql-bin.index", "var/lib/mysql/my.cnf", "etc/mysql/my.cnf", "etc/my.cnf", "program files/mysql/mysql server 5.0/my.ini", "program files/mysql/mysql server 5.0/my.cnf", "program files/mysql/my.ini", "program files/mysql/my.cnf", "mysql/my.ini", "mysql/my.cnf", "mysql/bin/my.ini", "var/postgresql/log/postgresql.log", "usr/internet/pgsql/data/postmaster.log", "usr/local/pgsql/data/postgresql.log", "usr/local/pgsql/data/pg_log", "postgresql/log/pgadmin.log", "var/lib/pgsql/data/postgresql.conf", "var/postgresql/db/postgresql.conf", "var/nm2/postgresql.conf", "usr/local/pgsql/data/postgresql.conf", "usr/local/pgsql/data/pg_hba.conf", "usr/internet/pgsql/data/pg_hba.conf", "usr/local/pgsql/data/passwd", "usr/local/pgsql/bin/pg_passwd", "etc/postgresql/postgresql.conf", "etc/postgresql/pg_hba.conf", "home/postgres/data/postgresql.conf", "home/postgres/data/pg_version", "home/postgres/data/pg_ident.conf", "home/postgres/data/pg_hba.conf", "program files/postgresql/8.3/data/pg_hba.conf", "program files/postgresql/8.3/data/pg_ident.conf", "program files/postgresql/8.3/data/postgresql.conf", "program files/postgresql/8.4/data/pg_hba.conf", "program files/postgresql/8.4/data/pg_ident.conf", "program files/postgresql/8.4/data/postgresql.conf", "program files/postgresql/9.0/data/pg_hba.conf", "program files/postgresql/9.0/data/pg_ident.conf", "program files/postgresql/9.0/data/postgresql.conf", "program files/postgresql/9.1/data/pg_hba.conf", "program files/postgresql/9.1/data/pg_ident.conf", "program files/postgresql/9.1/data/postgresql.conf", "wamp/logs/access.log", "wamp/logs/apache_error.log", "wamp/logs/genquery.log", "wamp/logs/mysql.log", "wamp/logs/slowquery.log", "wamp/bin/apache/apache2.2.22/logs/access.log", "wamp/bin/apache/apache2.2.22/logs/error.log", "wamp/bin/apache/apache2.2.21/logs/access.log", "wamp/bin/apache/apache2.2.21/logs/error.log", "wamp/bin/mysql/mysql5.5.24/data/mysql-bin.index", "wamp/bin/mysql/mysql5.5.16/data/mysql-bin.index", "wamp/bin/apache/apache2.2.21/conf/httpd.conf", "wamp/bin/apache/apache2.2.22/conf/httpd.conf", "wamp/bin/apache/apache2.2.21/wampserver.conf", "wamp/bin/apache/apache2.2.22/wampserver.conf", "wamp/bin/apache/apache2.2.22/conf/wampserver.conf", "wamp/bin/mysql/mysql5.5.24/my.ini", "wamp/bin/mysql/mysql5.5.24/wampserver.conf", "wamp/bin/mysql/mysql5.5.16/my.ini", "wamp/bin/mysql/mysql5.5.16/wampserver.conf", "wamp/bin/php/php5.3.8/php.ini", "wamp/bin/php/php5.4.3/php.ini", "xampp/apache/logs/access.log", "xampp/apache/logs/error.log", "xampp/mysql/data/mysql-bin.index", "xampp/mysql/data/mysql.err", "xampp/mysql/data/{host}.err", "xampp/sendmail/sendmail.log", "xampp/apache/conf/httpd.conf", "xampp/filezillaftp/filezilla server.xml", "xampp/mercurymail/mercury.ini", "xampp/php/php.ini", "xampp/phpmyadmin/config.inc.php", "xampp/sendmail/sendmail.ini", "xampp/webalizer/webalizer.conf", "opt/lampp/etc/httpd.conf", "xampp/htdocs/aca.txt", "xampp/htdocs/admin.php", "xampp/htdocs/leer.txt", "usr/local/apache/logs/audit_log", "usr/local/apache2/logs/audit_log", "logs/security_debug_log", "logs/security_log", "usr/local/apache/conf/modsec.conf", "usr/local/apache2/conf/modsec.conf", "winnt/system32/logfiles/msftpsvc", "winnt/system32/logfiles/msftpsvc1", "winnt/system32/logfiles/msftpsvc2", "windows/system32/logfiles/msftpsvc", "windows/system32/logfiles/msftpsvc1", "windows/system32/logfiles/msftpsvc2", "etc/logrotate.d/proftpd", "www/logs/proftpd.system.log", "etc/pam.d/proftpd", "etc/proftp.conf", "etc/protpd/proftpd.conf", "etc/vhcs2/proftpd/proftpd.conf", "etc/proftpd/modules.conf", "etc/vsftpd.chroot_list", "etc/logrotate.d/vsftpd.log", "etc/vsftpd/vsftpd.conf", "etc/vsftpd.conf", "etc/chrootusers", "var/adm/log/xferlog", "etc/wu-ftpd/ftpaccess", "etc/wu-ftpd/ftphosts", "etc/wu-ftpd/ftpusers", "logs/pure-ftpd.log", "usr/sbin/pure-config.pl", "usr/etc/pure-ftpd.conf", "etc/pure-ftpd/pure-ftpd.conf", "usr/local/etc/pure-ftpd.conf", "usr/local/etc/pureftpd.pdb", "usr/local/pureftpd/etc/pureftpd.pdb", "usr/local/pureftpd/sbin/pure-config.pl", "usr/local/pureftpd/etc/pure-ftpd.conf", "etc/pure-ftpd.conf", "etc/pure-ftpd/pure-ftpd.pdb", "etc/pureftpd.pdb", "etc/pureftpd.passwd", "etc/pure-ftpd/pureftpd.pdb", "usr/ports/ftp/pure-ftpd/pure-ftpd.conf", "usr/ports/ftp/pure-ftpd/pureftpd.pdb", "usr/ports/ftp/pure-ftpd/pureftpd.passwd", "usr/ports/net/pure-ftpd/pure-ftpd.conf", "usr/ports/net/pure-ftpd/pureftpd.pdb", "usr/ports/net/pure-ftpd/pureftpd.passwd", "usr/pkgsrc/net/pureftpd/pure-ftpd.conf", "usr/pkgsrc/net/pureftpd/pureftpd.pdb", "usr/pkgsrc/net/pureftpd/pureftpd.passwd", "usr/ports/contrib/pure-ftpd/pure-ftpd.conf", "usr/ports/contrib/pure-ftpd/pureftpd.pdb", "usr/ports/contrib/pure-ftpd/pureftpd.passwd", "usr/sbin/mudlogd", "etc/muddleftpd/mudlog", "etc/muddleftpd.com", "etc/muddleftpd/mudlogd.conf", "etc/muddleftpd/muddleftpd.conf", "usr/sbin/mudpasswd", "etc/muddleftpd/muddleftpd.passwd", "etc/muddleftpd/passwd", "etc/logrotate.d/ftp", "etc/ftpchroot", "etc/ftphosts", "etc/ftpusers", "winnt/system32/logfiles/smtpsvc", "winnt/system32/logfiles/smtpsvc1", "winnt/system32/logfiles/smtpsvc2", "winnt/system32/logfiles/smtpsvc3", "winnt/system32/logfiles/smtpsvc4", "winnt/system32/logfiles/smtpsvc5", "windows/system32/logfiles/smtpsvc", "windows/system32/logfiles/smtpsvc1", "windows/system32/logfiles/smtpsvc2", "windows/system32/logfiles/smtpsvc3", "windows/system32/logfiles/smtpsvc4", "windows/system32/logfiles/smtpsvc5", "etc/osxhttpd/osxhttpd.conf", "system/library/webobjects/adaptors/apache2.2/apache.conf", "etc/apache2/sites-available/default", "etc/apache2/sites-available/default-ssl", "etc/apache2/sites-enabled/000-default", "etc/apache2/sites-enabled/default", "etc/apache2/apache2.conf", "etc/apache2/ports.conf", "usr/local/etc/apache/httpd.conf", "usr/pkg/etc/httpd/httpd.conf", "usr/pkg/etc/httpd/httpd-default.conf", "usr/pkg/etc/httpd/httpd-vhosts.conf", "etc/httpd/mod_php.conf", "etc/httpd/extra/httpd-ssl.conf", "etc/rc.d/rc.httpd", "usr/local/apache/conf/httpd.conf.default", "usr/local/apache/conf/access.conf", "usr/local/apache22/conf/httpd.conf", "usr/local/apache22/httpd.conf", "usr/local/etc/apache22/conf/httpd.conf", "usr/local/apps/apache22/conf/httpd.conf", "etc/apache22/conf/httpd.conf", "etc/apache22/httpd.conf", "opt/apache22/conf/httpd.conf", "usr/local/etc/apache2/vhosts.conf", "usr/local/apache/conf/vhosts.conf", "usr/local/apache2/conf/vhosts.conf", "usr/local/apache/conf/vhosts-custom.conf", "usr/local/apache2/conf/vhosts-custom.conf", "etc/apache/default-server.conf", "etc/apache2/default-server.conf", "usr/local/apache2/conf/extra/httpd-ssl.conf", "usr/local/apache2/conf/ssl.conf", "etc/httpd/conf.d", "usr/local/etc/apache22/httpd.conf", "usr/local/etc/apache2/httpd.conf", "etc/apache2/httpd2.conf", "etc/apache2/ssl-global.conf", "etc/apache2/vhosts.d/00_default_vhost.conf", "apache/conf/httpd.conf", "etc/apache/httpd.conf", "etc/httpd/conf", "http/httpd.conf", "usr/local/apache1.3/conf/httpd.conf", "usr/local/etc/httpd/conf", "var/apache/conf/httpd.conf", "var/www/conf", "www/apache/conf/httpd.conf", "www/conf/httpd.conf", "etc/init.d", "etc/apache/access.conf", "etc/rc.conf", "www/logs/freebsddiary-error.log", "www/logs/freebsddiary-access_log", "library/webserver/documents/index.html", "library/webserver/documents/index.htm", "library/webserver/documents/default.html", "library/webserver/documents/default.htm", "library/webserver/documents/index.php", "library/webserver/documents/default.php", "usr/local/etc/webmin/miniserv.conf", "etc/webmin/miniserv.conf", "usr/local/etc/webmin/miniserv.users", "etc/webmin/miniserv.users", "winnt/system32/logfiles/w3svc/inetsvn1.log", "winnt/system32/logfiles/w3svc1/inetsvn1.log", "winnt/system32/logfiles/w3svc2/inetsvn1.log", "winnt/system32/logfiles/w3svc3/inetsvn1.log", "windows/system32/logfiles/w3svc/inetsvn1.log", "windows/system32/logfiles/w3svc1/inetsvn1.log", "windows/system32/logfiles/w3svc2/inetsvn1.log", "windows/system32/logfiles/w3svc3/inetsvn1.log", "apache/logs/error.log", "apache/logs/access.log", "apache2/logs/error.log", "apache2/logs/access.log", "logs/error.log", "logs/access.log", "etc/httpd/logs/access_log", "etc/httpd/logs/access.log", "etc/httpd/logs/error_log", "etc/httpd/logs/error.log", "usr/local/apache/logs/access_log", "usr/local/apache/logs/access.log", "usr/local/apache/logs/error_log", "usr/local/apache/logs/error.log", "usr/local/apache2/logs/access_log", "usr/local/apache2/logs/access.log", "usr/local/apache2/logs/error_log", "usr/local/apache2/logs/error.log", "var/www/logs/access_log", "var/www/logs/access.log", "var/www/logs/error_log", "var/www/logs/error.log", "opt/lampp/logs/access_log", "opt/lampp/logs/error_log", "opt/xampp/logs/access_log", "opt/xampp/logs/error_log", "opt/lampp/logs/access.log", "opt/lampp/logs/error.log", "opt/xampp/logs/access.log", "opt/xampp/logs/error.log", "program files/apache group/apache/logs/access.log", "program files/apache group/apache/logs/error.log", "program files/apache software foundation/apache2.2/logs/error.log", "program files/apache software foundation/apache2.2/logs/access.log", "opt/apache/apache.conf", "opt/apache/conf/apache.conf", "opt/apache2/apache.conf", "opt/apache2/conf/apache.conf", "opt/httpd/apache.conf", "opt/httpd/conf/apache.conf", "etc/httpd/apache.conf", "etc/apache2/apache.conf", "etc/httpd/conf/apache.conf", "usr/local/apache/apache.conf", "usr/local/apache/conf/apache.conf", "usr/local/apache2/apache.conf", "usr/local/apache2/conf/apache.conf", "usr/local/php/apache.conf.php", "usr/local/php4/apache.conf.php", "usr/local/php5/apache.conf.php", "usr/local/php/apache.conf", "usr/local/php4/apache.conf", "usr/local/php5/apache.conf", "private/etc/httpd/apache.conf", "opt/apache/apache2.conf", "opt/apache/conf/apache2.conf", "opt/apache2/apache2.conf", "opt/apache2/conf/apache2.conf", "opt/httpd/apache2.conf", "opt/httpd/conf/apache2.conf", "etc/httpd/apache2.conf", "etc/httpd/conf/apache2.conf", "usr/local/apache/apache2.conf", "usr/local/apache/conf/apache2.conf", "usr/local/apache2/apache2.conf", "usr/local/apache2/conf/apache2.conf", "usr/local/php/apache2.conf.php", "usr/local/php4/apache2.conf.php", "usr/local/php5/apache2.conf.php", "usr/local/php/apache2.conf", "usr/local/php4/apache2.conf", "usr/local/php5/apache2.conf", "private/etc/httpd/apache2.conf", "usr/local/apache/conf/httpd.conf", "usr/local/apache2/conf/httpd.conf", "etc/httpd/conf/httpd.conf", "etc/apache/apache.conf", "etc/apache/conf/httpd.conf", "etc/apache2/httpd.conf", "usr/apache2/conf/httpd.conf", "usr/apache/conf/httpd.conf", "usr/local/etc/apache/conf/httpd.conf", "usr/local/apache/httpd.conf", "usr/local/apache2/httpd.conf", "usr/local/httpd/conf/httpd.conf", "usr/local/etc/apache2/conf/httpd.conf", "usr/local/etc/httpd/conf/httpd.conf", "usr/local/apps/apache2/conf/httpd.conf", "usr/local/apps/apache/conf/httpd.conf", "usr/local/php/httpd.conf.php", "usr/local/php4/httpd.conf.php", "usr/local/php5/httpd.conf.php", "usr/local/php/httpd.conf", "usr/local/php4/httpd.conf", "usr/local/php5/httpd.conf", "etc/apache2/conf/httpd.conf", "etc/http/conf/httpd.conf", "etc/httpd/httpd.conf", "etc/http/httpd.conf", "etc/httpd.conf", "opt/apache/conf/httpd.conf", "opt/apache2/conf/httpd.conf", "var/www/conf/httpd.conf", "private/etc/httpd/httpd.conf", "private/etc/httpd/httpd.conf.default", "etc/apache2/vhosts.d/default_vhost.include", "etc/apache2/conf.d/charset", "etc/apache2/conf.d/security", "etc/apache2/envvars", "etc/apache2/mods-available/autoindex.conf", "etc/apache2/mods-available/deflate.conf", "etc/apache2/mods-available/dir.conf", "etc/apache2/mods-available/mem_cache.conf", "etc/apache2/mods-available/mime.conf", "etc/apache2/mods-available/proxy.conf", "etc/apache2/mods-available/setenvif.conf", "etc/apache2/mods-available/ssl.conf", "etc/apache2/mods-enabled/alias.conf", "etc/apache2/mods-enabled/deflate.conf", "etc/apache2/mods-enabled/dir.conf", "etc/apache2/mods-enabled/mime.conf", "etc/apache2/mods-enabled/negotiation.conf", "etc/apache2/mods-enabled/php5.conf", "etc/apache2/mods-enabled/status.conf", "program files/apache group/apache/conf/httpd.conf", "program files/apache group/apache2/conf/httpd.conf", "program files/xampp/apache/conf/apache.conf", "program files/xampp/apache/conf/apache2.conf", "program files/xampp/apache/conf/httpd.conf", "program files/apache group/apache/apache.conf", "program files/apache group/apache/conf/apache.conf", "program files/apache group/apache2/conf/apache.conf", "program files/apache group/apache/apache2.conf", "program files/apache group/apache/conf/apache2.conf", "program files/apache group/apache2/conf/apache2.conf", "program files/apache software foundation/apache2.2/conf/httpd.conf", "volumes/macintosh_hd1/opt/httpd/conf/httpd.conf", "volumes/macintosh_hd1/opt/apache/conf/httpd.conf", "volumes/macintosh_hd1/opt/apache2/conf/httpd.conf", "volumes/macintosh_hd1/usr/local/php/httpd.conf.php", "volumes/macintosh_hd1/usr/local/php4/httpd.conf.php", "volumes/macintosh_hd1/usr/local/php5/httpd.conf.php", "volumes/webbackup/opt/apache2/conf/httpd.conf", "volumes/webbackup/private/etc/httpd/httpd.conf", "volumes/webbackup/private/etc/httpd/httpd.conf.default", "usr/local/etc/apache/vhosts.conf", "usr/local/jakarta/tomcat/conf/jakarta.conf", "usr/local/jakarta/tomcat/conf/server.xml", "usr/local/jakarta/tomcat/conf/context.xml", "usr/local/jakarta/tomcat/conf/workers.properties", "usr/local/jakarta/tomcat/conf/logging.properties", "usr/local/jakarta/dist/tomcat/conf/jakarta.conf", "usr/local/jakarta/dist/tomcat/conf/server.xml", "usr/local/jakarta/dist/tomcat/conf/context.xml", "usr/local/jakarta/dist/tomcat/conf/workers.properties", "usr/local/jakarta/dist/tomcat/conf/logging.properties", "usr/share/tomcat6/conf/server.xml", "usr/share/tomcat6/conf/context.xml", "usr/share/tomcat6/conf/workers.properties", "usr/share/tomcat6/conf/logging.properties", "var/cpanel/tomcat.options", "usr/local/jakarta/tomcat/logs/catalina.out", "usr/local/jakarta/tomcat/logs/catalina.err", "opt/tomcat/logs/catalina.out", "opt/tomcat/logs/catalina.err", "usr/share/logs/catalina.out", "usr/share/logs/catalina.err", "usr/share/tomcat/logs/catalina.out", "usr/share/tomcat/logs/catalina.err", "usr/share/tomcat6/logs/catalina.out", "usr/share/tomcat6/logs/catalina.err", "usr/local/apache/logs/mod_jk.log", "usr/local/jakarta/tomcat/logs/mod_jk.log", "usr/local/jakarta/dist/tomcat/logs/mod_jk.log", "opt/[jboss]/server/default/conf/jboss-minimal.xml", "opt/[jboss]/server/default/conf/jboss-service.xml", "opt/[jboss]/server/default/conf/jndi.properties", "opt/[jboss]/server/default/conf/log4j.xml", "opt/[jboss]/server/default/conf/login-config.xml", "opt/[jboss]/server/default/conf/standardjaws.xml", "opt/[jboss]/server/default/conf/standardjboss.xml", "opt/[jboss]/server/default/conf/server.log.properties", "opt/[jboss]/server/default/deploy/jboss-logging.xml", "usr/local/[jboss]/server/default/conf/jboss-minimal.xml", "usr/local/[jboss]/server/default/conf/jboss-service.xml", "usr/local/[jboss]/server/default/conf/jndi.properties", "usr/local/[jboss]/server/default/conf/log4j.xml", "usr/local/[jboss]/server/default/conf/login-config.xml", "usr/local/[jboss]/server/default/conf/standardjaws.xml", "usr/local/[jboss]/server/default/conf/standardjboss.xml", "usr/local/[jboss]/server/default/conf/server.log.properties", "usr/local/[jboss]/server/default/deploy/jboss-logging.xml", "private/tmp/[jboss]/server/default/conf/jboss-minimal.xml", "private/tmp/[jboss]/server/default/conf/jboss-service.xml", "private/tmp/[jboss]/server/default/conf/jndi.properties", "private/tmp/[jboss]/server/default/conf/log4j.xml", "private/tmp/[jboss]/server/default/conf/login-config.xml", "private/tmp/[jboss]/server/default/conf/standardjaws.xml", "private/tmp/[jboss]/server/default/conf/standardjboss.xml", "private/tmp/[jboss]/server/default/conf/server.log.properties", "private/tmp/[jboss]/server/default/deploy/jboss-logging.xml", "tmp/[jboss]/server/default/conf/jboss-minimal.xml", "tmp/[jboss]/server/default/conf/jboss-service.xml", "tmp/[jboss]/server/default/conf/jndi.properties", "tmp/[jboss]/server/default/conf/log4j.xml", "tmp/[jboss]/server/default/conf/login-config.xml", "tmp/[jboss]/server/default/conf/standardjaws.xml", "tmp/[jboss]/server/default/conf/standardjboss.xml", "tmp/[jboss]/server/default/conf/server.log.properties", "tmp/[jboss]/server/default/deploy/jboss-logging.xml", "program files/[jboss]/server/default/conf/jboss-minimal.xml", "program files/[jboss]/server/default/conf/jboss-service.xml", "program files/[jboss]/server/default/conf/jndi.properties", "program files/[jboss]/server/default/conf/log4j.xml", "program files/[jboss]/server/default/conf/login-config.xml", "program files/[jboss]/server/default/conf/standardjaws.xml", "program files/[jboss]/server/default/conf/standardjboss.xml", "program files/[jboss]/server/default/conf/server.log.properties", "program files/[jboss]/server/default/deploy/jboss-logging.xml", "[jboss]/server/default/conf/jboss-minimal.xml", "[jboss]/server/default/conf/jboss-service.xml", "[jboss]/server/default/conf/jndi.properties", "[jboss]/server/default/conf/log4j.xml", "[jboss]/server/default/conf/login-config.xml", "[jboss]/server/default/conf/standardjaws.xml", "[jboss]/server/default/conf/standardjboss.xml", "[jboss]/server/default/conf/server.log.properties", "[jboss]/server/default/deploy/jboss-logging.xml", "opt/[jboss]/server/default/log/server.log", "opt/[jboss]/server/default/log/boot.log", "usr/local/[jboss]/server/default/log/server.log", "usr/local/[jboss]/server/default/log/boot.log", "private/tmp/[jboss]/server/default/log/server.log", "private/tmp/[jboss]/server/default/log/boot.log", "tmp/[jboss]/server/default/log/server.log", "tmp/[jboss]/server/default/log/boot.log", "program files/[jboss]/server/default/log/server.log", "program files/[jboss]/server/default/log/boot.log", "[jboss]/server/default/log/server.log", "[jboss]/server/default/log/boot.log", "var/lighttpd.log", "var/logs/access.log", "usr/local/apache2/logs/lighttpd.error.log", "usr/local/apache2/logs/lighttpd.log", "usr/local/apache/logs/lighttpd.error.log", "usr/local/apache/logs/lighttpd.log", "usr/local/lighttpd/log/lighttpd.error.log", "usr/local/lighttpd/log/access.log", "usr/home/<USER>/var/log/lighttpd.error.log", "usr/home/<USER>/var/log/apache.log", "home/user/lighttpd/lighttpd.conf", "usr/home/<USER>/lighttpd/lighttpd.conf", "etc/lighttpd/lighthttpd.conf", "usr/local/etc/lighttpd.conf", "usr/local/lighttpd/conf/lighttpd.conf", "usr/local/etc/lighttpd.conf.new", "var/www/.lighttpdpassword", "logs/access_log", "logs/error_log", "etc/nginx/nginx.conf", "usr/local/etc/nginx/nginx.conf", "usr/local/nginx/conf/nginx.conf", "usr/local/zeus/web/global.cfg", "usr/local/zeus/web/log/errors", "opt/lsws/conf/httpd_conf.xml", "usr/local/lsws/conf/httpd_conf.xml", "opt/lsws/logs/error.log", "opt/lsws/logs/access.log", "usr/local/lsws/logs/error.log", "usr/local/logs/access.log", "usr/local/samba/lib/log.user", "usr/local/logs/samba.log", "etc/samba/netlogon", "etc/smbpasswd", "etc/smb.conf", "etc/samba/dhcp.conf", "etc/samba/smb.conf", "etc/samba/samba.conf", "etc/samba/smb.conf.user", "etc/samba/smbpasswd", "etc/samba/smbusers", "etc/samba/private/smbpasswd", "usr/local/etc/smb.conf", "usr/local/samba/lib/smb.conf.user", "etc/dhcp3/dhclient.conf", "etc/dhcp3/dhcpd.conf", "etc/dhcp/dhclient.conf", "program files/vidalia bundle/polipo/polipo.conf", "etc/tor/tor-tsocks.conf", "etc/stunnel/stunnel.conf", "etc/tsocks.conf", "etc/tinyproxy/tinyproxy.conf", "etc/miredo-server.conf", "etc/miredo.conf", "etc/miredo/miredo-server.conf", "etc/miredo/miredo.conf", "etc/wicd/dhclient.conf.template.default", "etc/wicd/manager-settings.conf", "etc/wicd/wired-settings.conf", "etc/wicd/wireless-settings.conf", "etc/ipfw.rules", "etc/ipfw.conf", "etc/firewall.rules", "winnt/system32/logfiles/firewall/pfirewall.log", "winnt/system32/logfiles/firewall/pfirewall.log.old", "windows/system32/logfiles/firewall/pfirewall.log", "windows/system32/logfiles/firewall/pfirewall.log.old", "etc/clamav/clamd.conf", "etc/clamav/freshclam.conf", "etc/x11/xorg.conf", "etc/x11/xorg.conf-vesa", "etc/x11/xorg.conf-vmware", "etc/x11/xorg.conf.beforevmwaretoolsinstall", "etc/x11/xorg.conf.orig", "etc/bluetooth/input.conf", "etc/bluetooth/main.conf", "etc/bluetooth/network.conf", "etc/bluetooth/rfcomm.conf", "etc/bash_completion.d/debconf", "root/.bash_logout", "root/.bash_history", "root/.bash_config", "root/.bashrc", "etc/bash.bashrc", "var/adm/syslog", "var/adm/sulog", "var/adm/utmp", "var/adm/utmpx", "var/adm/wtmp", "var/adm/wtmpx", "var/adm/lastlog/username", "usr/spool/lp/log", "var/adm/lp/lpd-errs", "usr/lib/cron/log", "var/adm/loginlog", "var/adm/pacct", "var/adm/dtmp", "var/adm/acct/sum/loginlog", "var/adm/x0msgs", "var/adm/crash/vmcore", "var/adm/crash/unix", "etc/newsyslog.conf", "var/adm/qacct", "var/adm/ras/errlog", "var/adm/ras/bootlog", "var/adm/cron/log", "etc/utmp", "etc/security/lastlog", "etc/security/failedlogin", "usr/spool/mqueue/syslog", "var/adm/messages", "var/adm/aculogs", "var/adm/aculog", "var/adm/vold.log", "var/adm/log/asppp.log", "var/lp/logs/lpsched", "var/lp/logs/lpnet", "var/lp/logs/requests", "var/cron/log", "var/saf/_log", "var/saf/port/log", "tmp/access.log", "etc/sensors.conf", "etc/sensors3.conf", "etc/host.conf", "etc/pam.conf", "etc/resolv.conf", "etc/apt/apt.conf", "etc/inetd.conf", "etc/syslog.conf", "etc/sysctl.conf", "etc/sysctl.d/10-console-messages.conf", "etc/sysctl.d/10-network-security.conf", "etc/sysctl.d/10-process-security.conf", "etc/sysctl.d/wine.sysctl.conf", "etc/security/access.conf", "etc/security/group.conf", "etc/security/limits.conf", "etc/security/namespace.conf", "etc/security/pam_env.conf", "etc/security/sepermit.conf", "etc/security/time.conf", "etc/ssh/sshd_config", "etc/adduser.conf", "etc/deluser.conf", "etc/avahi/avahi-daemon.conf", "etc/ca-certificates.conf", "etc/ca-certificates.conf.dpkg-old", "etc/casper.conf", "etc/chkrootkit.conf", "etc/debconf.conf", "etc/dns2tcpd.conf", "etc/e2fsck.conf", "etc/esound/esd.conf", "etc/etter.conf", "etc/fuse.conf", "etc/foremost.conf", "etc/hdparm.conf", "etc/kernel-img.conf", "etc/kernel-pkg.conf", "etc/ld.so.conf", "etc/ltrace.conf", "etc/mail/sendmail.conf", "etc/manpath.config", "etc/kbd/config", "etc/ldap/ldap.conf", "etc/logrotate.conf", "etc/mtools.conf", "etc/smi.conf", "etc/updatedb.conf", "etc/pulse/client.conf", "usr/share/adduser/adduser.conf", "etc/hostname", "etc/networks", "etc/timezone", "etc/modules", "etc/passwd", "etc/shadow", "etc/fstab", "etc/motd", "etc/hosts", "etc/group", "etc/alias", "etc/crontab", "etc/crypttab", "etc/exports", "etc/mtab", "etc/hosts.allow", "etc/hosts.deny", "etc/os-release", "etc/password.master", "etc/profile", "etc/default/grub", "etc/resolvconf/update-libc.d/sendmail", "etc/inittab", "etc/issue", "etc/issue.net", "etc/login.defs", "etc/sudoers", "etc/sysconfig/network-scripts/ifcfg-eth0", "etc/redhat-release", "etc/scw-release", "etc/system-release-cpe", "etc/debian_version", "etc/fedora-release", "etc/mandrake-release", "etc/slackware-release", "etc/suse-release", "etc/security/group", "etc/security/passwd", "etc/security/user", "etc/security/environ", "etc/security/limits", "etc/security/opasswd", "boot/grub/grub.cfg", "boot/grub/menu.lst", "root/.ksh_history", "root/.xauthority", "usr/lib/security/mkuser.default", "var/lib/squirrelmail/prefs/squirrelmail.log", "etc/squirrelmail/apache.conf", "etc/squirrelmail/config_local.php", "etc/squirrelmail/default_pref", "etc/squirrelmail/index.php", "etc/squirrelmail/config_default.php", "etc/squirrelmail/config.php", "etc/squirrelmail/filters_setup.php", "etc/squirrelmail/sqspell_config.php", "etc/squirrelmail/config/config.php", "etc/httpd/conf.d/squirrelmail.conf", "usr/share/squirrelmail/config/config.php", "private/etc/squirrelmail/config/config.php", "srv/www/htdos/squirrelmail/config/config.php", "var/www/squirrelmail/config/config.php", "var/www/html/squirrelmail/config/config.php", "var/www/html/squirrelmail-1.2.9/config/config.php", "usr/share/squirrelmail/plugins/squirrel_logger/setup.php", "usr/local/squirrelmail/www/readme", "windows/system32/drivers/etc/hosts", "windows/system32/drivers/etc/lmhosts.sam", "windows/system32/drivers/etc/networks", "windows/system32/drivers/etc/protocol", "windows/system32/drivers/etc/services", "/boot.ini", "windows/debug/netsetup.log", "windows/comsetup.log", "windows/repair/setup.log", "windows/setupact.log", "windows/setupapi.log", "windows/setuperr.log", "windows/updspapi.log", "windows/wmsetup.log", "windows/windowsupdate.log", "windows/odbc.ini", "usr/local/psa/admin/htdocs/domains/databases/phpmyadmin/libraries/config.default.php", "etc/apache2/conf.d/phpmyadmin.conf", "etc/phpmyadmin/config.inc.php", "etc/openldap/ldap.conf", "etc/cups/acroread.conf", "etc/cups/cupsd.conf", "etc/cups/cupsd.conf.default", "etc/cups/pdftops.conf", "etc/cups/printers.conf", "windows/system32/macromed/flash/flashinstall.log", "windows/system32/macromed/flash/install.log", "etc/cvs-cron.conf", "etc/cvs-pserver.conf", "etc/subversion/config", "etc/modprobe.d/vmware-tools.conf", "etc/updatedb.conf.beforevmwaretoolsinstall", "etc/vmware-tools/config", "etc/vmware-tools/tpvmlp.conf", "etc/vmware-tools/vmware-tools-libraries.conf", "var/log", "var/log/sw-cp-server/error_log", "var/log/sso/sso.log", "var/log/dpkg.log", "var/log/btmp", "var/log/utmp", "var/log/wtmp", "var/log/mysql/mysql-bin.log", "var/log/mysql/mysql-bin.index", "var/log/mysql/data/mysql-bin.index", "var/log/mysql.log", "var/log/mysql.err", "var/log/mysqlderror.log", "var/log/mysql/mysql.log", "var/log/mysql/mysql-slow.log", "var/log/mysql-bin.index", "var/log/data/mysql-bin.index", "var/log/postgresql/postgresql.log", "var/log/postgres/pg_backup.log", "var/log/postgres/postgres.log", "var/log/postgresql.log", "var/log/pgsql/pgsql.log", "var/log/postgresql/postgresql-8.1-main.log", "var/log/postgresql/postgresql-8.3-main.log", "var/log/postgresql/postgresql-8.4-main.log", "var/log/postgresql/postgresql-9.0-main.log", "var/log/postgresql/postgresql-9.1-main.log", "var/log/pgsql8.log", "var/log/postgresql/postgres.log", "var/log/pgsql_log", "var/log/postgresql/main.log", "var/log/cron", "var/log/postgres.log", "var/log/proftpd", "var/log/proftpd/xferlog.legacy", "var/log/proftpd.access_log", "var/log/proftpd.xferlog", "var/log/vsftpd.log", "var/log/xferlog", "var/log/pure-ftpd/pure-ftpd.log", "var/log/pureftpd.log", "var/log/muddleftpd", "var/log/muddleftpd.conf", "var/log/ftp-proxy/ftp-proxy.log", "var/log/ftp-proxy", "var/log/ftplog", "var/log/exim_mainlog", "var/log/exim/mainlog", "var/log/maillog", "var/log/exim_paniclog", "var/log/exim/paniclog", "var/log/exim/rejectlog", "var/log/exim_rejectlog", "var/log/webmin/miniserv.log", "var/log/httpd/access_log", "var/log/httpd/error_log", "var/log/httpd/access.log", "var/log/httpd/error.log", "var/log/apache/access_log", "var/log/apache/access.log", "var/log/apache/error_log", "var/log/apache/error.log", "var/log/apache2/access_log", "var/log/apache2/access.log", "var/log/apache2/error_log", "var/log/apache2/error.log", "var/log/access_log", "var/log/access.log", "var/log/error_log", "var/log/error.log", "var/log/tomcat6/catalina.out", "var/log/lighttpd.error.log", "var/log/lighttpd.access.log", "var/logs/access.log", "var/log/lighttpd/", "var/log/lighttpd/error.log", "var/log/lighttpd/access.www.log", "var/log/lighttpd/error.www.log", "var/log/lighttpd/access.log", "var/log/lighttpd/{domain}/access.log", "var/log/lighttpd/{domain}/error.log", "var/log/nginx/access_log", "var/log/nginx/error_log", "var/log/nginx/access.log", "var/log/nginx/error.log", "var/log/nginx.access_log", "var/log/nginx.error_log", "var/log/samba/log.smbd", "var/log/samba/log.nmbd", "var/log/samba.log", "var/log/samba.log1", "var/log/samba.log2", "var/log/log.smb", "var/log/ipfw.log", "var/log/ipfw", "var/log/ipfw/ipfw.log", "var/log/ipfw.today", "var/log/poplog", "var/log/authlog", "var/log/news.all", "var/log/news/news.all", "var/log/news/news.crit", "var/log/news/news.err", "var/log/news/news.notice", "var/log/news/suck.err", "var/log/news/suck.notice", "var/log/messages", "var/log/messages.1", "var/log/user.log", "var/log/user.log.1", "var/log/auth.log", "var/log/pm-powersave.log", "var/log/xorg.0.log", "var/log/daemon.log", "var/log/daemon.log.1", "var/log/kern.log", "var/log/kern.log.1", "var/log/mail.err", "var/log/mail.info", "var/log/mail.warn", "var/log/ufw.log", "var/log/boot.log", "var/log/syslog", "var/log/syslog.1", "var/log/squirrelmail.log", "var/log/apache2/squirrelmail.log", "var/log/apache2/squirrelmail.err.log", "var/log/mail.log", "var/log/vmware/hostd.log", "var/log/vmware/hostd-1.log", "/wp-config.php", "/wp-config.bak", "/wp-config.old", "/wp-config.temp", "/wp-config.tmp", "/wp-config.txt", "/config.yml", "/config_dev.yml", "/config_prod.yml", "/config_test.yml", "/parameters.yml", "/routing.yml", "/security.yml", "/services.yml", "sites/default/default.settings.php", "sites/default/settings.php", "sites/default/settings.local.php", "app/etc/local.xml", "/sftp-config.json", "/web.config", "includes/config.php", "includes/configure.php", "/config.inc.php", "/localsettings.php", "inc/config.php", "typo3conf/localconf.php", "config/app.php", "config/custom.php", "config/database.php", "/configuration.php", "/config.php", "var/mail/www-data", "etc/network/", "etc/init/", "inetpub/wwwroot/global.asa", "system32/inetsrv/config/applicationhost.config", "system32/inetsrv/config/administration.config", "system32/inetsrv/config/redirection.config", "system32/config/default", "system32/config/sam", "system32/config/system", "system32/config/software", "winnt/repair/sam._", "/package.json", "/package-lock.json", "/gruntfile.js", "/npm-debug.log", "/ormconfig.json", "/tsconfig.json", "/webpack.config.js", "/yarn.lock", "proc/0", "proc/1", "proc/2", "proc/3", "proc/4", "proc/5", "proc/6", "proc/7", "proc/8", "proc/9", "proc/acpi", "proc/asound", "proc/bootconfig", "proc/buddyinfo", "proc/bus", "proc/cgroups", "proc/cmdline", "proc/config.gz", "proc/consoles", "proc/cpuinfo", "proc/crypto", "proc/devices", "proc/diskstats", "proc/dma", "proc/docker", "proc/driver", "proc/dynamic_debug", "proc/execdomains", "proc/fb", "proc/filesystems", "proc/fs", "proc/interrupts", "proc/iomem", "proc/ioports", "proc/ipmi", "proc/irq", "proc/kallsyms", "proc/kcore", "proc/keys", "proc/keys", "proc/key-users", "proc/kmsg", "proc/kpagecgroup", "proc/kpagecount", "proc/kpageflags", "proc/latency_stats", "proc/loadavg", "proc/locks", "proc/mdstat", "proc/meminfo", "proc/misc", "proc/modules", "proc/mounts", "proc/mpt", "proc/mtd", "proc/mtrr", "proc/net", "proc/net/tcp", "proc/net/udp", "proc/pagetypeinfo", "proc/partitions", "proc/pressure", "proc/sched_debug", "proc/schedstat", "proc/scsi", "proc/self", "proc/self/cmdline", "proc/self/environ", "proc/self/fd/0", "proc/self/fd/1", "proc/self/fd/10", "proc/self/fd/11", "proc/self/fd/12", "proc/self/fd/13", "proc/self/fd/14", "proc/self/fd/15", "proc/self/fd/2", "proc/self/fd/3", "proc/self/fd/4", "proc/self/fd/5", "proc/self/fd/6", "proc/self/fd/7", "proc/self/fd/8", "proc/self/fd/9", "proc/self/mounts", "proc/self/stat", "proc/self/status", "proc/slabinfo", "proc/softirqs", "proc/stat", "proc/swaps", "proc/sys", "proc/sysrq-trigger", "proc/sysvipc", "proc/thread-self", "proc/timer_list", "proc/timer_stats", "proc/tty", "proc/uptime", "proc/version", "proc/version_signature", "proc/vmallocinfo", "proc/vmstat", "proc/zoneinfo", "sys/block", "sys/bus", "sys/class", "sys/dev", "sys/devices", "sys/firmware", "sys/fs", "sys/hypervisor", "sys/kernel", "sys/module", "sys/power", "windows\\win.ini", "default\\ntuser.dat", "/var/run/secrets/kubernetes.io/serviceaccount"], "options": {"enforce_word_boundary": true}}, "operator": "phrase_match"}], "transformers": ["lowercase", "normalizePath"]}, {"id": "crs-931-110", "name": "RFI: Common RFI Vulnerable Parameter Name used w/ URL Payload", "tags": {"type": "rfi", "crs_id": "931110", "category": "attack_attempt", "cwe": "98", "capec": "1000/152/175/253/193", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}], "regex": "(?:\\binclude\\s*\\([^)]*|mosConfig_absolute_path|_CONF\\[path\\]|_SERVER\\[DOCUMENT_ROOT\\]|GALLERY_BASEDIR|path\\[docroot\\]|appserv_root|config\\[root_dir\\])=(?:file|ftps?|https?)://", "options": {"min_length": 15}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-931-120", "name": "RFI: URL Payload Used w/Trailing Question Mark Character (?)", "tags": {"type": "rfi", "crs_id": "931120", "category": "attack_attempt", "cwe": "98", "capec": "1000/152/175/253/193", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^(?i:file|ftps?)://.*?\\?+$", "options": {"case_sensitive": true, "min_length": 4}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-932-160", "name": "Remote Command Execution: Unix Shell Code Found", "tags": {"type": "command_injection", "crs_id": "932160", "category": "attack_attempt", "cwe": "77", "capec": "1000/152/248/88", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "options": {"enforce_word_boundary": true}, "list": ["${cdpath}", "${dirstack}", "${home}", "${hostname}", "${ifs}", "${oldpwd}", "${ostype}", "${path}", "${pwd}", "$cdpath", "$dirstack", "$home", "$hostname", "$ifs", "$oldpwd", "$ostype", "$pwd", "dev/fd/", "dev/null", "dev/stderr", "dev/stdin", "dev/stdout", "dev/tcp/", "dev/udp/", "dev/zero", "etc/master.passwd", "etc/pwd.db", "etc/shells", "etc/spwd.db", "proc/self/", "bin/7z", "bin/7za", "bin/7zr", "bin/ab", "bin/agetty", "bin/ansible-playbook", "bin/apt", "bin/apt-get", "bin/ar", "bin/aria2c", "bin/arj", "bin/arp", "bin/as", "bin/ascii-xfr", "bin/ascii85", "bin/ash", "bin/aspell", "bin/at", "bin/atobm", "bin/awk", "bin/base32", "bin/base64", "bin/basenc", "bin/bash", "bin/bpftrace", "bin/bridge", "bin/bundler", "bin/bunzip2", "bin/busctl", "bin/busybox", "bin/byebug", "bin/bzcat", "bin/bzcmp", "bin/bzdiff", "bin/bzegrep", "bin/bzexe", "bin/bzfgrep", "bin/bzgrep", "bin/bzip2", "bin/bzip2recover", "bin/bzless", "bin/bzmore", "bin/bzz", "bin/c89", "bin/c99", "bin/cancel", "bin/capsh", "bin/cat", "bin/cc", "bin/certbot", "bin/check_by_ssh", "bin/check_cups", "bin/check_log", "bin/check_memory", "bin/check_raid", "bin/check_ssl_cert", "bin/check_statusfile", "bin/chmod", "bin/choom", "bin/chown", "bin/chroot", "bin/clang", "bin/clang++", "bin/cmp", "bin/cobc", "bin/column", "bin/comm", "bin/composer", "bin/core_perl/zipdetails", "bin/cowsay", "bin/cowthink", "bin/cp", "bin/cpan", "bin/cpio", "bin/cpulimit", "bin/crash", "bin/crontab", "bin/csh", "bin/csplit", "bin/csvtool", "bin/cupsfilter", "bin/curl", "bin/cut", "bin/dash", "bin/date", "bin/dd", "bin/dev/fd/", "bin/dev/null", "bin/dev/stderr", "bin/dev/stdin", "bin/dev/stdout", "bin/dev/tcp/", "bin/dev/udp/", "bin/dev/zero", "bin/dialog", "bin/diff", "bin/dig", "bin/dmesg", "bin/dmidecode", "bin/dmsetup", "bin/dnf", "bin/docker", "bin/dosbox", "bin/dpkg", "bin/du", "bin/dvips", "bin/easy_install", "bin/eb", "bin/echo", "bin/ed", "bin/efax", "bin/emacs", "bin/env", "bin/eqn", "bin/es", "bin/esh", "bin/etc/group", "bin/etc/master.passwd", "bin/etc/passwd", "bin/etc/pwd.db", "bin/etc/shadow", "bin/etc/shells", "bin/etc/spwd.db", "bin/ex", "bin/exiftool", "bin/expand", "bin/expect", "bin/expr", "bin/facter", "bin/fetch", "bin/file", "bin/find", "bin/finger", "bin/fish", "bin/flock", "bin/fmt", "bin/fold", "bin/fping", "bin/ftp", "bin/gawk", "bin/gcc", "bin/gcore", "bin/gdb", "bin/gem", "bin/genie", "bin/genisoimage", "bin/ghc", "bin/ghci", "bin/gimp", "bin/ginsh", "bin/git", "bin/grc", "bin/grep", "bin/gtester", "bin/gunzip", "bin/gzexe", "bin/gzip", "bin/hd", "bin/head", "bin/hexdump", "bin/highlight", "bin/hping3", "bin/iconv", "bin/id", "bin/iftop", "bin/install", "bin/ionice", "bin/ip", "bin/irb", "bin/ispell", "bin/jjs", "bin/join", "bin/journalctl", "bin/jq", "bin/jrunscript", "bin/knife", "bin/ksh", "bin/ksshell", "bin/latex", "bin/ld", "bin/ldconfig", "bin/less", "bin/lftp", "bin/ln", "bin/loginctl", "bin/logsave", "bin/look", "bin/lp", "bin/ls", "bin/ltrace", "bin/lua", "bin/lualatex", "bin/luatex", "bin/lwp-download", "bin/lwp-request", "bin/lz", "bin/lz4", "bin/lz4c", "bin/lz4cat", "bin/lzcat", "bin/lzcmp", "bin/lzdiff", "bin/lzegrep", "bin/lzfgrep", "bin/lzgrep", "bin/lzless", "bin/lzma", "bin/lzmadec", "bin/lzmainfo", "bin/lzmore", "bin/mail", "bin/make", "bin/man", "bin/mawk", "bin/mkfifo", "bin/mknod", "bin/more", "bin/mosquitto", "bin/mount", "bin/msgattrib", "bin/msgcat", "bin/msgconv", "bin/msgfilter", "bin/msgmerge", "bin/msguniq", "bin/mtr", "bin/mv", "bin/mysql", "bin/nano", "bin/nasm", "bin/nawk", "bin/nc", "bin/ncat", "bin/neofetch", "bin/nice", "bin/nl", "bin/nm", "bin/nmap", "bin/node", "bin/nohup", "bin/npm", "bin/nroff", "bin/nsenter", "bin/octave", "bin/od", "bin/openssl", "bin/openvpn", "bin/openvt", "bin/opkg", "bin/paste", "bin/pax", "bin/pdb", "bin/pdflatex", "bin/pdftex", "bin/pdksh", "bin/perf", "bin/perl", "bin/pg", "bin/php", "bin/php-cgi", "bin/php5", "bin/php7", "bin/pic", "bin/pico", "bin/pidstat", "bin/pigz", "bin/pip", "bin/pkexec", "bin/pkg", "bin/pr", "bin/printf", "bin/proc/self/", "bin/pry", "bin/ps", "bin/psed", "bin/psftp", "bin/psql", "bin/ptx", "bin/puppet", "bin/pxz", "bin/python", "bin/python2", "bin/python3", "bin/rake", "bin/rbash", "bin/rc", "bin/readelf", "bin/red", "bin/redcarpet", "bin/restic", "bin/rev", "bin/rlogin", "bin/rlwrap", "bin/rpm", "bin/rpmquery", "bin/rsync", "bin/ruby", "bin/run-mailcap", "bin/run-parts", "bin/rview", "bin/rvim", "bin/sash", "bin/sbin/capsh", "bin/sbin/logsave", "bin/sbin/service", "bin/sbin/start-stop-daemon", "bin/scp", "bin/screen", "bin/script", "bin/sed", "bin/service", "bin/setarch", "bin/sftp", "bin/sg", "bin/sh", "bin/shuf", "bin/sleep", "bin/slsh", "bin/smbclient", "bin/snap", "bin/socat", "bin/soelim", "bin/sort", "bin/split", "bin/sqlite3", "bin/ss", "bin/ssh", "bin/ssh-keygen", "bin/ssh-keyscan", "bin/sshpass", "bin/start-stop-daemon", "bin/stdbuf", "bin/strace", "bin/strings", "bin/su", "bin/sysctl", "bin/systemctl", "bin/systemd-resolve", "bin/tac", "bin/tail", "bin/tar", "bin/task", "bin/taskset", "bin/tbl", "bin/tclsh", "bin/tcpdump", "bin/tcsh", "bin/tee", "bin/telnet", "bin/tex", "bin/tftp", "bin/tic", "bin/time", "bin/timedatectl", "bin/timeout", "bin/tmux", "bin/top", "bin/troff", "bin/tshark", "bin/ul", "bin/uname", "bin/uncompress", "bin/unexpand", "bin/uniq", "bin/unlz4", "bin/unlzma", "bin/unpigz", "bin/unrar", "bin/unshare", "bin/unxz", "bin/unzip", "bin/unzstd", "bin/update-alternatives", "bin/uudecode", "bin/uuencode", "bin/valgrind", "bin/vi", "bin/view", "bin/vigr", "bin/vim", "bin/vimdiff", "bin/vipw", "bin/virsh", "bin/volatility", "bin/wall", "bin/watch", "bin/wc", "bin/wget", "bin/whiptail", "bin/who", "bin/whoami", "bin/whois", "bin/wireshark", "bin/wish", "bin/xargs", "bin/xelatex", "bin/xetex", "bin/xmodmap", "bin/xmore", "bin/xpad", "bin/xxd", "bin/xz", "bin/xzcat", "bin/xzcmp", "bin/xzdec", "bin/xzdiff", "bin/xzegrep", "bin/xzfgrep", "bin/xzgrep", "bin/xzless", "bin/xzmore", "bin/yarn", "bin/yelp", "bin/yes", "bin/yum", "bin/zathura", "bin/zip", "bin/zipcloak", "bin/zipcmp", "bin/zipdetails", "bin/zipgrep", "bin/zipinfo", "bin/zipmerge", "bin/zipnote", "bin/zipsplit", "bin/ziptool", "bin/zsh", "bin/z<PERSON>elim", "bin/zstd", "bin/zstdcat", "bin/zstdgrep", "bin/zstdless", "bin/zstdmt", "bin/zypper"]}, "operator": "phrase_match"}], "transformers": ["lowercase", "cmdLine"]}, {"id": "crs-932-171", "name": "Remote Command Execution: Shellshock (CVE-2014-6271)", "tags": {"type": "command_injection", "crs_id": "932171", "category": "attack_attempt", "cwe": "77", "capec": "1000/152/248/88", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^\\(\\s*\\)\\s+{", "options": {"case_sensitive": true, "min_length": 4}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-932-180", "name": "Restricted File Upload Attempt", "tags": {"type": "command_injection", "crs_id": "932180", "category": "attack_attempt", "cwe": "706", "capec": "1000/225/122/17/177", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["x-filename"]}, {"address": "server.request.headers.no_cookies", "key_path": ["x_filename"]}, {"address": "server.request.headers.no_cookies", "key_path": ["x-file-name"]}], "list": [".htaccess", ".htdigest", ".htpasswd", "wp-config.php", "config.yml", "config_dev.yml", "config_prod.yml", "config_test.yml", "parameters.yml", "routing.yml", "security.yml", "services.yml", "default.settings.php", "settings.php", "settings.local.php", "local.xml", ".env"], "options": {"enforce_word_boundary": true}}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-933-111", "name": "PHP Injection Attack: PHP Script File Upload Found", "tags": {"type": "unrestricted_file_upload", "crs_id": "933111", "category": "attack_attempt", "cwe": "434", "capec": "1000/225/122/17/650", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["x-filename"]}, {"address": "server.request.headers.no_cookies", "key_path": ["x_filename"]}, {"address": "server.request.headers.no_cookies", "key_path": ["x.filename"]}, {"address": "server.request.headers.no_cookies", "key_path": ["x-file-name"]}], "regex": ".*\\.(?:php\\d*|phtml)\\..*$", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "crs-933-130", "name": "PHP Injection Attack: Global Variables Found", "tags": {"type": "php_code_injection", "crs_id": "933130", "category": "attack_attempt", "cwe": "94", "capec": "1000/225/122/17/650", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "options": {"enforce_word_boundary": true}, "list": ["$globals", "$_cookie", "$_env", "$_files", "$_get", "$_post", "$_request", "$_server", "$_session", "$argc", "$argv", "$http_\\u200bresponse_\\u200bheader", "$php_\\u200berrormsg", "$http_cookie_vars", "$http_env_vars", "$http_get_vars", "$http_post_files", "$http_post_vars", "$http_raw_post_data", "$http_request_vars", "$http_server_vars"]}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-933-131", "name": "PHP Injection Attack: HTTP Headers Values Found", "tags": {"type": "php_code_injection", "crs_id": "933131", "category": "attack_attempt", "cwe": "94", "capec": "1000/225/122/17/650", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:HTTP_(?:ACCEPT(?:_(?:ENCODING|LANGUAGE|CHARSET))?|(?:X_FORWARDED_FO|REFERE)R|(?:USER_AGEN|HOS)T|CONNECTION|KEEP_ALIVE)|PATH_(?:TRANSLATED|INFO)|ORIG_PATH_INFO|QUERY_STRING|REQUEST_URI|AUTH_TYPE)", "options": {"case_sensitive": true, "min_length": 9}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-933-140", "name": "PHP Injection Attack: I/O Stream Found", "tags": {"type": "php_code_injection", "crs_id": "933140", "category": "attack_attempt", "cwe": "94", "capec": "1000/225/122/17/650", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "php://(?:std(?:in|out|err)|(?:in|out)put|fd|memory|temp|filter)", "options": {"min_length": 8}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-933-150", "name": "PHP Injection Attack: High-Risk PHP Function Name Found", "tags": {"type": "php_code_injection", "crs_id": "933150", "category": "attack_attempt", "cwe": "94", "capec": "1000/225/122/17/650", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "list": ["__halt_compiler", "apache_child_terminate", "base64_decode", "bzdecompress", "call_user_func", "call_user_func_array", "call_user_method", "call_user_method_array", "convert_uudecode", "file_get_contents", "file_put_contents", "fsockopen", "get_class_methods", "get_class_vars", "get_defined_constants", "get_defined_functions", "get_defined_vars", "gzdecode", "gzinflate", "g<PERSON>ncomp<PERSON>", "include_once", "invokeargs", "pcntl_exec", "pcntl_fork", "pfsockopen", "posix_getcwd", "posix_getpwuid", "posix_getuid", "posix_uname", "reflectionfunction", "require_once", "shell_exec", "str_rot13", "sys_get_temp_dir", "wp_remote_fopen", "wp_remote_get", "wp_remote_head", "wp_remote_post", "wp_remote_request", "wp_safe_remote_get", "wp_safe_remote_head", "wp_safe_remote_post", "wp_safe_remote_request", "zlib_decode"], "options": {"enforce_word_boundary": true}}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-933-160", "name": "PHP Injection Attack: High-Risk PHP Function Call Found", "tags": {"type": "php_code_injection", "crs_id": "933160", "category": "attack_attempt", "cwe": "94", "capec": "1000/225/122/17/650", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:s(?:e(?:t(?:_(?:e(?:xception|rror)_handler|magic_quotes_runtime|include_path)|defaultstub)|ssion_s(?:et_save_handler|tart))|qlite_(?:(?:(?:unbuffered|single|array)_)?query|create_(?:aggregate|function)|p?open|exec)|tr(?:eam_(?:context_create|socket_client)|ipc?slashes|rev)|implexml_load_(?:string|file)|ocket_c(?:onnect|reate)|h(?:ow_sourc|a1_fil)e|pl_autoload_register|ystem)|p(?:r(?:eg_(?:replace(?:_callback(?:_array)?)?|match(?:_all)?|split)|oc_(?:(?:terminat|clos|nic)e|get_status|open)|int_r)|o(?:six_(?:get(?:(?:e[gu]|g)id|login|pwnam)|mk(?:fifo|nod)|ttyname|kill)|pen)|hp(?:_(?:strip_whitespac|unam)e|version|info)|g_(?:(?:execut|prepar)e|connect|query)|a(?:rse_(?:ini_file|str)|ssthru)|utenv)|r(?:unkit_(?:function_(?:re(?:defin|nam)e|copy|add)|method_(?:re(?:defin|nam)e|copy|add)|constant_(?:redefine|add))|e(?:(?:gister_(?:shutdown|tick)|name)_function|ad(?:(?:gz)?file|_exif_data|dir))|awurl(?:de|en)code)|i(?:mage(?:createfrom(?:(?:jpe|pn)g|x[bp]m|wbmp|gif)|(?:jpe|pn)g|g(?:d2?|if)|2?wbmp|xbm)|s_(?:(?:(?:execut|write?|read)ab|fi)le|dir)|ni_(?:get(?:_all)?|set)|terator_apply|ptcembed)|g(?:et(?:_(?:c(?:urrent_use|fg_va)r|meta_tags)|my(?:[gpu]id|inode)|(?:lastmo|cw)d|imagesize|env)|z(?:(?:(?:defla|wri)t|encod|fil)e|compress|open|read)|lob)|a(?:rray_(?:u(?:intersect(?:_u?assoc)?|diff(?:_u?assoc)?)|intersect_u(?:assoc|key)|diff_u(?:assoc|key)|filter|reduce|map)|ssert(?:_options)?|tob)|h(?:tml(?:specialchars(?:_decode)?|_entity_decode|entities)|(?:ash(?:_(?:update|hmac))?|ighlight)_file|e(?:ader_register_callback|x2bin))|f(?:i(?:le(?:(?:[acm]tim|inod)e|(?:_exist|perm)s|group)?|nfo_open)|tp_(?:nb_(?:ge|pu)|connec|ge|pu)t|(?:unction_exis|pu)ts|write|open)|o(?:b_(?:get_(?:c(?:ontents|lean)|flush)|end_(?:clean|flush)|clean|flush|start)|dbc_(?:result(?:_all)?|exec(?:ute)?|connect)|pendir)|m(?:b_(?:ereg(?:_(?:replace(?:_callback)?|match)|i(?:_replace)?)?|parse_str)|(?:ove_uploaded|d5)_file|ethod_exists|ysql_query|kdir)|e(?:x(?:if_(?:t(?:humbnail|agname)|imagetype|read_data)|ec)|scapeshell(?:arg|cmd)|rror_reporting|val)|c(?:url_(?:file_create|exec|init)|onvert_uuencode|reate_function|hr)|u(?:n(?:serialize|pack)|rl(?:de|en)code|[ak]?sort)|b(?:(?:son_(?:de|en)|ase64_en)code|zopen|toa)|(?:json_(?:de|en)cod|debug_backtrac|tmpfil)e|var_dump)(?:\\s|/\\*.*\\*/|//.*|#.*|\\\"|')*\\((?:(?:\\s|/\\*.*\\*/|//.*|#.*)*(?:\\$\\w+|[A-Z\\d]\\w*|\\w+\\(.*\\)|\\\\?\"(?:[^\"]|\\\\\"|\"\"|\"\\+\")*\\\\?\"|\\\\?'(?:[^']|''|'\\+')*\\\\?')(?:\\s|/\\*.*\\*/|//.*|#.*)*(?:(?:::|\\.|->)(?:\\s|/\\*.*\\*/|//.*|#.*)*\\w+(?:\\(.*\\))?)?,)*(?:(?:\\s|/\\*.*\\*/|//.*|#.*)*(?:\\$\\w+|[A-Z\\d]\\w*|\\w+\\(.*\\)|\\\\?\"(?:[^\"]|\\\\\"|\"\"|\"\\+\")*\\\\?\"|\\\\?'(?:[^']|''|'\\+')*\\\\?')(?:\\s|/\\*.*\\*/|//.*|#.*)*(?:(?:::|\\.|->)(?:\\s|/\\*.*\\*/|//.*|#.*)*\\w+(?:\\(.*\\))?)?)?\\)\\s*(?:[;\\.)}\\]|\\\\]|\\?>|%>|$)", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-933-170", "name": "PHP Injection Attack: Serialized Object Injection", "tags": {"type": "php_code_injection", "crs_id": "933170", "category": "attack_attempt", "cwe": "502", "capec": "1000/152/586", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies"}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "[oOcC]:\\d+:\\\".+?\\\":\\d+:{[\\W\\w]*}", "options": {"case_sensitive": true, "min_length": 12}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-933-200", "name": "PHP Injection Attack: Wrapper scheme detected", "tags": {"type": "php_code_injection", "crs_id": "933200", "category": "attack_attempt", "cwe": "502", "capec": "1000/152/586", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:(?:bzip|ssh)2|z(?:lib|ip)|(?:ph|r)ar|expect|glob|ogg)://", "options": {"case_sensitive": true, "min_length": 6}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-934-100", "name": "Node.js Injection Attack 1/2", "tags": {"type": "js_code_injection", "crs_id": "934100", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:(?:l(?:(?:utimes|chmod)(?:Sync)?|(?:stat|ink)Sync)|w(?:rite(?:(?:File|v)(?:Sync)?|Sync)|atchFile)|u(?:n(?:watchFile|linkSync)|times(?:Sync)?)|s(?:(?:ymlink|tat)Sync|pawn(?:File|Sync))|ex(?:ec(?:File(?:Sync)?|Sync)|istsSync)|a(?:ppendFile|ccess)(?:Sync)?|(?:Caveat|Inode)s|open(?:dir)?Sync|new\\s+Function|Availability|\\beval)\\s*\\(|m(?:ain(?:Module\\s*(?:\\W*\\s*(?:constructor|require)|\\[)|\\s*(?:\\W*\\s*(?:constructor|require)|\\[))|kd(?:temp(?:Sync)?|irSync)\\s*\\(|odule\\.exports\\s*=)|c(?:(?:(?:h(?:mod|own)|lose)Sync|reate(?:Write|Read)Stream|p(?:Sync)?)\\s*\\(|o(?:nstructor\\s*(?:\\W*\\s*_load|\\[)|pyFile(?:Sync)?\\s*\\())|f(?:(?:(?:s(?:(?:yncS)?|tatS)|datas(?:yncS)?)ync|ch(?:mod|own)(?:Sync)?)\\s*\\(|u(?:nction\\s*\\(\\s*\\)\\s*{|times(?:Sync)?\\s*\\())|r(?:e(?:(?:ad(?:(?:File|link|dir)?Sync|v(?:Sync)?)|nameSync)\\s*\\(|quire\\s*(?:\\W*\\s*main|\\[))|m(?:Sync)?\\s*\\()|process\\s*(?:\\W*\\s*(?:mainModule|binding)|\\[)|t(?:his\\.constructor|runcateSync\\s*\\()|_(?:\\$\\$ND_FUNC\\$\\$_|_js_function)|global\\s*(?:\\W*\\s*process|\\[)|String\\s*\\.\\s*fromCharCode|binding\\s*\\[)", "options": {"case_sensitive": true, "min_length": 3}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-934-101", "name": "Node.js Injection Attack 2/2", "tags": {"type": "js_code_injection", "crs_id": "934101", "category": "attack_attempt", "confidence": "1", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:w(?:atch|rite)|(?:spaw|ope)n|exists|close|fork|read)\\s*\\(", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-941-110", "name": "XSS Filter - Category 1: Script Tag Vector", "tags": {"type": "xss", "crs_id": "941110", "category": "attack_attempt", "cwe": "80", "capec": "1000/152/242/63/591", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "server.request.headers.no_cookies", "key_path": ["referer"]}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<script[^>]*>[\\s\\S]*?", "options": {"case_sensitive": false, "min_length": 8}}, "operator": "match_regex"}], "transformers": ["removeNulls", "urlDecodeUni"]}, {"id": "crs-941-120", "name": "XSS Filter - Category 2: Event Handler Vector", "tags": {"type": "xss", "crs_id": "941120", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "server.request.headers.no_cookies", "key_path": ["referer"]}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bon(?:d(?:r(?:ag(?:en(?:ter|d)|leave|start|over)?|op)|urationchange|blclick)|s(?:e(?:ek(?:ing|ed)|arch|lect)|u(?:spend|bmit)|talled|croll|how)|m(?:ouse(?:(?:lea|mo)ve|o(?:ver|ut)|enter|down|up)|essage)|p(?:a(?:ge(?:hide|show)|(?:st|us)e)|lay(?:ing)?|rogress|aste|ointer(?:cancel|down|enter|leave|move|out|over|rawupdate|up))|c(?:anplay(?:through)?|o(?:ntextmenu|py)|hange|lick|ut)|a(?:nimation(?:iteration|start|end)|(?:fterprin|bor)t|uxclick|fterscriptexecute)|t(?:o(?:uch(?:cancel|start|move|end)|ggle)|imeupdate)|f(?:ullscreen(?:change|error)|ocus(?:out|in)?|inish)|(?:(?:volume|hash)chang|o(?:ff|n)lin)e|b(?:efore(?:unload|print)|lur)|load(?:ed(?:meta)?data|start|end)?|r(?:es(?:ize|et)|atechange)|key(?:press|down|up)|w(?:aiting|heel)|in(?:valid|put)|e(?:nded|rror)|unload)[\\s\\x0B\\x09\\x0C\\x3B\\x2C\\x28\\x3B]*?=[^=]", "options": {"min_length": 8}}, "operator": "match_regex"}], "transformers": ["removeNulls", "urlDecodeUni"]}, {"id": "crs-941-140", "name": "XSS Filter - Category 4: Javascript URI Vector", "tags": {"type": "xss", "crs_id": "941140", "category": "attack_attempt", "cwe": "84", "capec": "1000/152/242/63/591/244", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "server.request.headers.no_cookies", "key_path": ["referer"]}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "[a-z]+=(?:[^:=]+:.+;)*?[^:=]+:url\\(javascript", "options": {"min_length": 18}}, "operator": "match_regex"}], "transformers": ["removeNulls", "urlDecodeUni"]}, {"id": "crs-941-170", "name": "NoScript XSS InjectionChecker: Attribute Injection", "tags": {"type": "xss", "crs_id": "941170", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "server.request.headers.no_cookies", "key_path": ["referer"]}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:\\W|^)(?:javascript:(?:[\\s\\S]+[=\\x5c\\(\\[\\.<]|[\\s\\S]*?(?:\\bname\\b|\\x5c[ux]\\d)))|@\\W*?i\\W*?m\\W*?p\\W*?o\\W*?r\\W*?t\\W*?(?:/\\*[\\s\\S]*?)?(?:[\\\"']|\\W*?u\\W*?r\\W*?l[\\s\\S]*?\\()|[^-]*?-\\W*?m\\W*?o\\W*?z\\W*?-\\W*?b\\W*?i\\W*?n\\W*?d\\W*?i\\W*?n\\W*?g[^:]*?:\\W*?u\\W*?r\\W*?l[\\s\\S]*?\\(", "options": {"min_length": 6}}, "operator": "match_regex"}], "transformers": ["removeNulls", "urlDecodeUni"]}, {"id": "crs-941-180", "name": "Node-Validator Deny List Keywords", "tags": {"type": "xss", "crs_id": "941180", "category": "attack_attempt", "cwe": "79", "capec": "1000/152/242/63/591", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "options": {"enforce_word_boundary": true}, "list": ["document.cookie", "document.write", ".parentnode", ".innerhtml", "window.location", "-moz-binding"]}, "operator": "phrase_match"}], "transformers": ["removeNulls", "lowercase"]}, {"id": "crs-941-200", "name": "IE XSS Filters - Attack Detected via vmlframe tag", "tags": {"type": "xss", "crs_id": "941200", "category": "attack_attempt", "cwe": "80", "capec": "1000/152/242/63/591", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:<.*[:]?vmlframe.*?[\\s/+]*?src[\\s/+]*=)", "options": {"case_sensitive": true, "min_length": 13}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-210", "name": "IE XSS Filters - Obfuscated Attack Detected via javascript injection", "tags": {"type": "xss", "crs_id": "941210", "category": "attack_attempt", "cwe": "80", "capec": "1000/152/242/63/591", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:(?:j|&#x?0*(?:74|4A|106|6A);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:a|&#x?0*(?:65|41|97|61);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:v|&#x?0*(?:86|56|118|76);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:a|&#x?0*(?:65|41|97|61);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:s|&#x?0*(?:83|53|115|73);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:c|&#x?0*(?:67|43|99|63);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:r|&#x?0*(?:82|52|114|72);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:i|&#x?0*(?:73|49|105|69);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:p|&#x?0*(?:80|50|112|70);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:t|&#x?0*(?:84|54|116|74);?)(?:\\t|\\n|\\r|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?::|&(?:#x?0*(?:58|3A);?|colon;)).)", "options": {"case_sensitive": true, "min_length": 12}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-220", "name": "IE XSS Filters - Obfuscated Attack Detected via vbscript injection", "tags": {"type": "xss", "crs_id": "941220", "category": "attack_attempt", "cwe": "80", "capec": "1000/152/242/63/591", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:(?:v|&#x?0*(?:86|56|118|76);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:b|&#x?0*(?:66|42|98|62);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:s|&#x?0*(?:83|53|115|73);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:c|&#x?0*(?:67|43|99|63);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:r|&#x?0*(?:82|52|114|72);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:i|&#x?0*(?:73|49|105|69);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:p|&#x?0*(?:80|50|112|70);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?:t|&#x?0*(?:84|54|116|74);?)(?:\\t|&(?:#x?0*(?:9|13|10|A|D);?|tab;|newline;))*(?::|&(?:#x?0*(?:58|3A);?|colon;)).)", "options": {"case_sensitive": true, "min_length": 10}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-230", "name": "IE XSS Filters - Attack Detected via embed tag", "tags": {"type": "xss", "crs_id": "941230", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<EMBED[\\s/+].*?(?:src|type).*?=", "options": {"min_length": 11}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-240", "name": "IE XSS Filters - Attack Detected via import tag", "tags": {"type": "xss", "crs_id": "941240", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<[?]?import[\\s/+\\S]*?implementation[\\s/+]*?=", "options": {"case_sensitive": true, "min_length": 22}}, "operator": "match_regex"}], "transformers": ["lowercase", "removeNulls"]}, {"id": "crs-941-270", "name": "IE XSS Filters - Attack Detected via link tag", "tags": {"type": "xss", "crs_id": "941270", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<LINK[\\s/+].*?href[\\s/+]*=", "options": {"min_length": 11}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-280", "name": "IE XSS Filters - Attack Detected via base tag", "tags": {"type": "xss", "crs_id": "941280", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<BASE[\\s/+].*?href[\\s/+]*=", "options": {"min_length": 11}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-290", "name": "IE XSS Filters - Attack Detected via applet tag", "tags": {"type": "xss", "crs_id": "941290", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<APPLET[\\s/+>]", "options": {"min_length": 8}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-300", "name": "IE XSS Filters - Attack Detected via object tag", "tags": {"type": "xss", "crs_id": "941300", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<OBJECT[\\s/+].*?(?:type|codetype|classid|code|data)[\\s/+]*=", "options": {"min_length": 13}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "crs-941-350", "name": "UTF-7 Encoding IE XSS - Attack Detected", "tags": {"type": "xss", "crs_id": "941350", "category": "attack_attempt", "cwe": "87", "capec": "1000/152/242/63/591/199", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\+ADw-.*(?:\\+AD4-|>)|<.*\\+AD4-", "options": {"case_sensitive": true, "min_length": 6}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-941-360", "name": "JSFuck / Hieroglyphy obfuscation detected", "tags": {"type": "xss", "crs_id": "941360", "category": "attack_attempt", "cwe": "87", "capec": "1000/152/242/63/591/199", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "![!+ ]\\[\\]", "options": {"case_sensitive": true, "min_length": 4}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-941-390", "name": "Javascript method detected", "tags": {"type": "xss", "crs_id": "941390", "category": "attack_attempt", "confidence": "1", "cwe": "79", "capec": "1000/152/242/63/591", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?i:eval|settimeout|setinterval|new\\s+Function|alert|prompt)[\\s+]*\\([^\\)]", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-100", "name": "SQL Injection Attack Detected via libinjection", "tags": {"type": "sql_injection", "crs_id": "942100", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}]}, "operator": "is_sqli"}], "transformers": ["removeNulls"]}, {"id": "crs-942-160", "name": "Detects blind sqli tests using sleep() or benchmark()", "tags": {"type": "sql_injection", "crs_id": "942160", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66/7", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:sleep\\(\\s*?\\d*?\\s*?\\)|benchmark\\(.*?\\,.*?\\))", "options": {"case_sensitive": true, "min_length": 7}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-240", "name": "Detects MySQL charset switch and MSSQL DoS attempts", "tags": {"type": "sql_injection", "crs_id": "942240", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66/7", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:[\\\"'`](?:;*?\\s*?waitfor\\s+(?:delay|time)\\s+[\\\"'`]|;.*?:\\s*?goto)|alter\\s*?\\w+.*?cha(?:racte)?r\\s+set\\s+\\w+)", "options": {"min_length": 7}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-250", "name": "Detects MATCH AGAINST, MERGE and EXECUTE IMMEDIATE injections", "tags": {"type": "sql_injection", "crs_id": "942250", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:merge.*?using\\s*?\\(|execute\\s*?immediate\\s*?[\\\"'`]|match\\s*?[\\w(?:),+-]+\\s*?against\\s*?\\()", "options": {"case_sensitive": true, "min_length": 11}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-270", "name": "Basic SQL injection", "tags": {"type": "sql_injection", "crs_id": "942270", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "union.*?select.*?from", "options": {"min_length": 15}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-280", "name": "SQL Injection with delay functions", "tags": {"type": "sql_injection", "crs_id": "942280", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66/7", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:;\\s*?shutdown\\s*?(?:[#;{]|\\/\\*|--)|waitfor\\s*?delay\\s?[\\\"'`]+\\s?\\d|select\\s*?pg_sleep)", "options": {"min_length": 10}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-290", "name": "Finds basic MongoDB SQL injection attempts", "tags": {"type": "nosql_injection", "crs_id": "942290", "category": "attack_attempt", "cwe": "943", "capec": "1000/152/248/676", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:(?:\\[?\\$(?:(?:s(?:lic|iz)|wher)e|e(?:lemMatch|xists|q)|n(?:o[rt]|in?|e)|l(?:ike|te?)|t(?:ext|ype)|a(?:ll|nd)|jsonSchema|between|regex|x?or|div|mod)\\]?)\\b)", "options": {"case_sensitive": true, "min_length": 3}}, "operator": "match_regex"}], "transformers": ["keys_only"]}, {"id": "crs-942-360", "name": "Detects concatenated basic SQL injection and SQLLFI attempts", "tags": {"type": "sql_injection", "crs_id": "942360", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66/470", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:^[\\W\\d]+\\s*?(?:alter\\s*(?:a(?:(?:pplication\\s*rol|ggregat)e|s(?:ymmetric\\s*ke|sembl)y|u(?:thorization|dit)|vailability\\s*group)|c(?:r(?:yptographic\\s*provider|edential)|o(?:l(?:latio|um)|nversio)n|ertificate|luster)|s(?:e(?:rv(?:ice|er)|curity|quence|ssion|arch)|y(?:mmetric\\s*key|nonym)|togroup|chema)|m(?:a(?:s(?:ter\\s*key|k)|terialized)|e(?:ssage\\s*type|thod)|odule)|l(?:o(?:g(?:file\\s*group|in)|ckdown)|a(?:ngua|r)ge|ibrary)|t(?:(?:abl(?:espac)?|yp)e|r(?:igger|usted)|hreshold|ext)|p(?:a(?:rtition|ckage)|ro(?:cedur|fil)e|ermission)|d(?:i(?:mension|skgroup)|atabase|efault|omain)|r(?:o(?:l(?:lback|e)|ute)|e(?:sourc|mot)e)|f(?:u(?:lltext|nction)|lashback|oreign)|e(?:xte(?:nsion|rnal)|(?:ndpoi|ve)nt)|in(?:dex(?:type)?|memory|stance)|b(?:roker\\s*priority|ufferpool)|x(?:ml\\s*schema|srobject)|w(?:ork(?:load)?|rapper)|hi(?:erarchy|stogram)|o(?:perator|utline)|(?:nicknam|queu)e|us(?:age|er)|group|java|view)|union\\s*(?:(?:distin|sele)ct|all))\\b|\\b(?:(?:(?:trunc|cre|upd)at|renam)e|(?:inser|selec)t|de(?:lete|sc)|alter|load)\\s+(?:group_concat|load_file|char)\\b\\s*\\(?|[\\s(]load_file\\s*?\\(|[\\\"'`]\\s+regexp\\W)", "options": {"min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-942-500", "name": "MySQL in-line comment detected", "tags": {"type": "sql_injection", "crs_id": "942500", "category": "attack_attempt", "cwe": "89", "capec": "1000/152/248/66", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:/\\*[!+](?:[\\w\\s=_\\-(?:)]+)?\\*/)", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-943-100", "name": "Possible Session Fixation Attack: Setting Cookie Values in HTML", "tags": {"type": "http_protocol_violation", "crs_id": "943100", "category": "attack_attempt", "cwe": "384", "capec": "1000/225/21/593/61", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i:\\.cookie\\b.*?;\\W*?(?:expires|domain)\\W*?=|\\bhttp-equiv\\W+set-cookie\\b)", "options": {"case_sensitive": true, "min_length": 15}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-944-100", "name": "Remote Command Execution: Suspicious Java class detected", "tags": {"type": "java_code_injection", "crs_id": "944100", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "java\\.lang\\.(?:runtime|processbuilder)", "options": {"case_sensitive": true, "min_length": 17}}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "crs-944-110", "name": "Remote Command Execution: Java process spawn (CVE-2017-9805)", "tags": {"type": "java_code_injection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:unmarshaller|base64data|java\\.).*(?:runtime|processbuilder)", "options": {"case_sensitive": false, "min_length": 13}}, "operator": "match_regex"}], "transformers": []}, {"id": "crs-944-130", "name": "Suspicious Java class detected", "tags": {"type": "java_code_injection", "crs_id": "944130", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "list": ["com.opensymphony.xwork2", "com.sun.org.apache", "java.io.bufferedinputstream", "java.io.bufferedreader", "java.io.bytearrayinputstream", "java.io.bytearrayoutputstream", "java.io.chara<PERSON><PERSON><PERSON>", "java.io.datainputstream", "java.io.file", "java.io.fileoutputstream", "java.io.filepermission", "java.io.filewriter", "java.io.filterinputstream", "java.io.filteroutputstream", "java.io.filterreader", "java.io.inputstream", "java.io.inputstreamreader", "java.io.linenumberreader", "java.io.objectoutputstream", "java.io.outputstream", "java.io.pipedoutputstream", "java.io.pipedreader", "java.io.printstream", "java.io.pushbackinputstream", "java.io.reader", "java.io.stringreader", "java.lang.class", "java.lang.integer", "java.lang.number", "java.lang.object", "java.lang.process", "java.lang.reflect", "java.lang.runtime", "java.lang.string", "java.lang.stringbuilder", "java.lang.system", "javax.script.scriptenginemanager", "org.apache.commons", "org.apache.struts", "org.apache.struts2", "org.omg.corba", "java.beans.xmldecode"], "options": {"enforce_word_boundary": true}}, "operator": "phrase_match"}], "transformers": ["lowercase"]}, {"id": "crs-944-260", "name": "Remote Command Execution: Malicious class-loading payload", "tags": {"type": "java_code_injection", "crs_id": "944260", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "confidence": "1"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:class\\.module\\.classLoader\\.resources\\.context\\.parent\\.pipeline|springframework\\.context\\.support\\.FileSystemXmlApplicationContext)", "options": {"case_sensitive": true, "min_length": 58}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-000-001", "name": "Look for Cassandra injections", "tags": {"type": "nosql_injection", "category": "attack_attempt", "cwe": "943", "capec": "1000/152/248/676", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}, {"address": "server.request.headers.no_cookies"}], "regex": "\\ballow\\s+filtering\\b"}, "operator": "match_regex"}], "transformers": ["removeComments"]}, {"id": "dog-000-002", "name": "OGNL - Look for formatting injection patterns", "tags": {"type": "java_code_injection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}, {"address": "server.request.headers.no_cookies"}], "regex": "[#%$]{(?:[^}]+[^\\w\\s}\\-_][^}]+|\\d+-\\d+)}", "options": {"case_sensitive": true}}}], "transformers": []}, {"id": "dog-000-003", "name": "OGNL - Detect OGNL exploitation primitives", "tags": {"type": "java_code_injection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "[@#]ognl", "options": {"case_sensitive": true}}}], "transformers": []}, {"id": "dog-000-004", "name": "Spring4Shell - Attempts to exploit the Spring4shell vulnerability", "tags": {"type": "exploit_detection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "confidence": "1"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.body"}], "regex": "^class\\.module\\.classLoader\\.", "options": {"case_sensitive": false}}}], "transformers": ["keys_only"]}, {"id": "dog-000-005", "name": "Node.js: Prototype pollution through __proto__", "tags": {"type": "js_code_injection", "category": "attack_attempt", "cwe": "1321", "capec": "1000/152/242", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}], "regex": "^__proto__$"}, "operator": "match_regex"}], "transformers": ["keys_only"]}, {"id": "dog-000-006", "name": "Node.js: Prototype pollution through constructor.prototype", "tags": {"type": "js_code_injection", "category": "attack_attempt", "cwe": "1321", "capec": "1000/152/242", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}], "regex": "^constructor$"}, "operator": "match_regex"}, {"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}], "regex": "^prototype$"}, "operator": "match_regex"}], "transformers": ["keys_only"]}, {"id": "dog-000-007", "name": "Server side template injection: Velocity & Freemarker", "tags": {"type": "java_code_injection", "category": "attack_attempt", "cwe": "1336", "capec": "1000/152/242/19", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "#(?:set|foreach|macro|parse|if)\\(.*\\)|<#assign.*>"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-68x", "name": "xorbot", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "xorbot", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bmasjesu\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-001", "name": "BurpCollaborator OOB domain", "tags": {"type": "attack_tool", "category": "attack_attempt", "tool_name": "BurpCollaborator", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:burpcollaborator\\.net|oastify\\.com)\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-002", "name": "Qualys OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "Qualys", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bqualysperiscope\\.com\\b|\\.oscomm\\."}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-003", "name": "Probely OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "<PERSON><PERSON><PERSON>", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bprbly\\.win\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-004", "name": "Known malicious out-of-band interaction domain", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:webhook\\.site|\\.canarytokens\\.com|vii\\.one|act1on3\\.ru|gdsburp\\.com|arcticwolf\\.net|oob\\.li|htbiw\\.com|h4\\.vc|mochan\\.cloud|imshopping\\.com|bootstrapnodejs\\.com|mooo-ng\\.com|securitytrails\\.com|canyouhackit\\.io|7bae\\.xyz)\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-005", "name": "Known suspicious out-of-band interaction domain", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:\\.ngrok\\.io|requestbin\\.com|requestbin\\.net)\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-006", "name": "Rapid7 OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "Rapid7", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bappspidered\\.rapid7\\."}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-007", "name": "Interact.sh OOB domain", "tags": {"type": "attack_tool", "category": "attack_attempt", "tool_name": "interact.sh", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:interact\\.sh|oast\\.(?:pro|live|site|online|fun|me)|indusfacefinder\\.in|where\\.land|syhunt\\.net|tssrt\\.de|boardofcyber\\.io|assetnote-callback\\.com|praetorianlabs\\.dev|netspi\\.sh)\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-008", "name": "Netsparker OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "Netsparker", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b(?:\\.|(?:\\\\|&#)(?:0*46|x0*2e);)?r87(?:\\.|(?:\\\\|&#)(?:0*46|x0*2e);)(?:me|com)\\b", "options": {"case_sensitive": false, "min_length": 7}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-009", "name": "WhiteHat Security OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "WhiteHatSecurity", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bwhsec(?:\\.|(?:\\\\|&#)(?:0*46|x0*2e);)us\\b", "options": {"case_sensitive": false, "min_length": 8}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-010", "name": "Nessus OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "<PERSON><PERSON><PERSON>", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\b\\.nessus\\.org\\b", "options": {"case_sensitive": false, "min_length": 8}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-011", "name": "Watchtowr OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "Watchtowr", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bwatchtowr\\.com\\b", "options": {"case_sensitive": false, "min_length": 8}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-012", "name": "AppCheck NG OOB domain", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "tool_name": "AppCheckNG", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\bptst\\.io\\b", "options": {"case_sensitive": false, "min_length": 7}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-013", "name": "Public PoC for CVE-2025-24813", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "/iSee857/session", "options": {"case_sensitive": false, "min_length": 16}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-913-014", "name": "Exploit attempt for Next.js Middleware Exploit (CVE-2025-29927)", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["x-middleware-subrequest"]}], "regex": ".*", "options": {"min_length": 1}}, "operator": "match_regex"}, {"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["x-middleware-subrequest"]}], "regex": "[0-9a-fA-F]{40}|\\[\\w+\\]"}, "operator": "!match_regex"}], "transformers": []}, {"id": "dog-920-001", "name": "JWT authentication bypass", "tags": {"type": "http_protocol_violation", "category": "attack_attempt", "cwe": "287", "capec": "1000/225/115", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.cookies"}, {"address": "server.request.headers.no_cookies", "key_path": ["authorization"]}], "regex": "^(?:Bearer )?ey[A-Za-z0-9+_\\-/]*([QY][UW]x[Hn]Ij([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiAi[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]Ij([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDogI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]IiA6ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]Ij([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiAi[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciOiAi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciIDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgOiJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]IiA6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]Ij([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciOiJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgOiAi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]IjogI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]IiA6I[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6I[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6I[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciIDogI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]IiA6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]IiA6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ciO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6I[km]5[Pv][Tb][km][U-X]|[QY][UW]x[Hn]IiA6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ID([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gI[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yIgO([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[\\x2b\\x2f-9A-Za-z]ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*ICJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]I([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*IDoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]A6I[km]5[Pv][Tb][km][U-X]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]y([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiJ[Ou][Tb][02]5[Fl]|[QY][UW]x[Hn]Ijoi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z]{2}[159BFJNRVZdhlptx][Bh][Tb][EG]ci([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[048AEIMQUYcgkosw]gOiAi[Tb][km]9[Ou][RZ][Q-Za-f]|[\\x2b\\x2f-9A-Za-z][02EGUWkm]F[Ms][RZ]yI6([048ACEIMQSUYcgikoswy]|[\\x2b\\x2f-9A-Za-z]I)*[CSiy]Ai[Tb][km]9[Ou][RZ][Q-Za-f])[A-Za-z0-9+-/]*\\.[A-Za-z0-9+_\\-/]+\\.(?:[A-Za-z0-9+_\\-/]+)?$", "options": {"case_sensitive": true}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-931-001", "name": "RFI: URL Payload to well known RFI target", "tags": {"type": "rfi", "category": "attack_attempt", "cwe": "98", "capec": "1000/152/175/253/193", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^(?i:file|ftps?|https?).*/rfiinc\\.txt\\?+$", "options": {"case_sensitive": true, "min_length": 17}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-932-100", "name": "Shell spawn executing network command", "tags": {"type": "command_injection", "category": "attack_attempt", "cwe": "77", "capec": "1000/152/248/88", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:(?:['\"\\x60({|;&]|(?:^|['\"\\x60({|;&])(?:cmd(?:\\.exe)?\\s+(?:/\\w(?::\\w+)?\\s+)*))(?:ping|curl|wget|telnet)|\\bnslookup)[\\s,]", "options": {"case_sensitive": true, "min_length": 5}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-934-001", "name": "XXE - XML file loads external entity", "tags": {"type": "xxe", "category": "attack_attempt", "cwe": "91", "capec": "1000/152/248/250", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.body"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?:<\\?xml[^>]*>.*)<!ENTITY[^>]+SYSTEM\\s+[^>]+>", "options": {"case_sensitive": false, "min_length": 24}}, "operator": "match_regex"}], "transformers": []}, {"id": "dog-941-001", "name": "XSS in source property", "tags": {"type": "xss", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "server.request.headers.no_cookies", "key_path": ["referer"]}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "<(?:iframe|esi:include)(?:(?:\\s|/)*\\w+=[\"'\\w]+)*(?:\\s|/)*src(?:doc)?=[\"']?(?:data:|javascript:|http:|dns:|//)[^\\s'\"]+['\"]?", "options": {"min_length": 14}}, "operator": "match_regex"}], "transformers": ["removeNulls", "urlDecodeUni"]}, {"id": "dog-942-001", "name": "Blind XSS callback domains", "tags": {"type": "xss", "category": "attack_attempt", "cwe": "83", "capec": "1000/152/242/63/591/243", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "https?:\\/\\/(?:.*\\.)?(?:bxss\\.(?:in|me)|xss\\.ht|js\\.rip)", "options": {"case_sensitive": false}}, "operator": "match_regex"}], "transformers": []}, {"id": "nfd-000-001", "name": "Detect common directory discovery scans", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "phrase_match", "parameters": {"options": {"enforce_word_boundary": true}, "inputs": [{"address": "server.request.uri.raw"}], "list": ["/wordpress/", "/etc/", "/login.php", "/install.php", "/administrator", "/admin.php", "/wp-config", "/php<PERSON><PERSON>min", "/fckeditor", "/mysql", "/manager/html", ".htaccess", "/config.php", "/configuration", "/cgi-bin/php", "/search.php", "/tinymce", "/tiny_mce", "/settings.php", "../../..", "/install/", "/download.php", "/webdav", "/forum.php", "/user.php", "/style.php", "/jmx-console", "/modules.php", "/include.php", "/default.asp", "/help.php", "/database.yml", "/database.yml.pgsql", "/database.yml.sqlite3", "/database.yml.sqlite", "/database.yml.mysql", ".%2e/", "/view.php", "/header.php", "/search.asp", "%5c%5c", "/server/php/", "/invoker/jmxinvokerservlet", "/phpmyadmin/index.php", "/data/admin/allowurl.txt", "/verify.php", "/misc/ajax.js", "/.idea", "/module.php", "/backup.rar", "/backup.tar", "/backup.zip", "/backup.7z", "/backup.gz", "/backup.tgz", "/backup.tar.gz", "waitfor%20delay", "/calendar.php", "/news.php", "/dompdf.php", "))))))))))))))))", "/web.config", "tree.php", "/cgi-bin-sdb/printenv", "/comments.php", "/detail.asp", "/license.txt", "/admin.asp", "/auth.php", "/list.php", "/content.php", "/mod.php", "/mini.php", "/install.pgsql", "/install.mysql", "/install.sqlite", "/install.sqlite3", "/install.txt", "/install.md", "/doku.php", "/main.asp", "/myadmin", "/force-download.php", "/iisprotect/admin", "/.giti<PERSON>re", "/print.php", "/common.php", "/mainfile.php", "/functions.php", "/scripts/setup.php", "/faq.php", "/op/op.login.php", "/home.php", "/includes/hnmain.inc.php3", "/preview.php", "/dump.rar", "/dump.tar", "/dump.zip", "/dump.7z", "/dump.gz", "/dump.tgz", "/dump.tar.gz", "/thumbnail.php", "/sendcard.php", "/global.asax", "/directory.php", "/footer.php", "/error.asp", "/forum.asp", "/save.php", "/htmlsax3.php", "/adm/krgourl.php", "/includes/converter.inc.php", "/nucleus/libs/pluginadmin.php", "/base_qry_common.php", "/fileadmin", "/bitrix/admin/", "/adm.php", "/util/barcode.php", "/action.php", "/rss.asp", "/downloads.php", "/page.php", "/snarf_ajax.php", "/fck/editor", "/sendmail.php", "/detail.php", "/iframe.php", "/swfupload.swf", "/jenkins/login", "/phpmyadmin/main.php", "/phpmyadmin/scripts/setup.php", "/user/index.php", "/checkout.php", "/process.php", "/ks_inc/ajax.js", "/export.php", "/register.php", "/cart.php", "/console.php", "/friend.php", "/readmsg.php", "/install.asp", "/dagent/downloadreport.asp", "/system/index.php", "/core/changelog.txt", "/js/util.js", "/interna.php", "/gallery.php", "/links.php", "/data/admin/ver.txt", "/language/zh-cn.xml", "/productdetails.asp", "/admin/template/article_more/config.htm", "/components/com_moofaq/includes/file_includer.php", "/licence.txt", "/rss.xsl", "/vtigerservice.php", "/mysql/main.php", "/passwiki.php", "/scr/soustab.php", "/global.php", "/email.php", "/user.asp", "/msd", "/products.php", "/cultbooking.php", "/cron.php", "/static/js/admincp.js", "/comment.php", "/maintainers", "/modules/plain/adminpart/addplain.php", "/wp-content/plugins/ungallery/source_vuln.php", "/upgrade.txt", "/category.php", "/index_logged.php", "/members.asp", "/script/html.js", "/images/ad.js", "/awstats/awstats.pl", "/includes/esqueletos/skel_null.php", "/modules/profile/user.php", "/window_top.php", "/openbrowser.php", "/thread.php", "tinfoil_xss", "/includes/include.php", "/urheber.php", "/header.inc.php", "/mysqldumper", "/display.php", "/website.php", "/stats.php", "/assets/plugins/mp3_id/mp3_id.php", "/siteminderagent/forms/smpwservices.fcc", "/eval-stdin.php"]}}], "transformers": ["lowercase"]}, {"id": "nfd-000-002", "name": "Detect failed attempt to fetch readme files", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "readme\\.[\\.a-z0-9]+$", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-003", "name": "Detect failed attempt to fetch Java EE resource files", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "^(?:.*web\\-inf)(?:.*web\\.xml).*$", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-004", "name": "Detect failed attempt to fetch code files", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "\\.(java|pyc?|rb|class)\\b", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-005", "name": "Detect failed attempt to fetch source code archives", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "\\.(sql|log|ndb|gz|zip|tar\\.gz|tar|regVV|reg|conf|bz2|ini|db|war|bat|inc|btr|server|ds|conf|config|admin|master|sln|bak)\\b(?:[^.]|$)", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-006", "name": "Detect failed attempt to fetch sensitive files", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "\\.(cgi|bat|dll|exe|key|cert|crt|pem|der|pkcs|pkcs|pkcs[0-9]*|nsf|jsa|war|java|class|vb|vba|so|git|svn|hg|cvs)([?#&/]|$)", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-007", "name": "Detect failed attempt to fetch archives", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "/[\\d\\-_]*\\.(rar|tar|zip|7z|gz|tgz|tar.gz)", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-008", "name": "Detect failed attempt to trigger incorrect application behavior", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "(/(administrator/components/com.*\\.php|response\\.write\\(.+\\))|select\\(.+\\)from|\\(.*sleep\\(.+\\)|(%[a-zA-Z0-9]{2}[a-zA-Z]{0,1})+\\))", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-009", "name": "Detect failed attempt to leak the structure of the application", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "/(login\\.rol|LICENSE|[\\w-]+\\.(plx|pwd))$", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "nfd-000-010", "name": "Detect failed attempts to find API documentation", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "0", "module": "waf"}, "conditions": [{"operator": "match_regex", "parameters": {"inputs": [{"address": "server.response.status"}], "regex": "^404$", "options": {"case_sensitive": true}}}, {"operator": "match_regex", "parameters": {"inputs": [{"address": "server.request.uri.raw"}], "regex": "(?:^|/)(?:swagger|api[-/]?docs?|openapi)\\b", "options": {"case_sensitive": false}}}], "transformers": []}, {"id": "rasp-930-100", "name": "Local file inclusion exploit", "tags": {"type": "lfi", "category": "vulnerability_trigger", "cwe": "22", "capec": "1000/255/153/126", "confidence": "1", "module": "rasp"}, "conditions": [{"parameters": {"resource": [{"address": "server.io.fs.file"}], "params": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}]}, "operator": "lfi_detector@v2"}], "transformers": [], "on_match": ["stack_trace"]}, {"id": "rasp-932-100", "name": "Shell command injection exploit", "tags": {"type": "command_injection", "category": "vulnerability_trigger", "cwe": "77", "capec": "1000/152/248/88", "confidence": "1", "module": "rasp"}, "conditions": [{"parameters": {"resource": [{"address": "server.sys.shell.cmd"}], "params": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}]}, "operator": "shi_detector"}], "transformers": [], "on_match": ["stack_trace"]}, {"id": "rasp-932-110", "name": "OS command injection exploit", "tags": {"type": "command_injection", "category": "vulnerability_trigger", "cwe": "77", "capec": "1000/152/248/88", "confidence": "1", "module": "rasp"}, "conditions": [{"parameters": {"resource": [{"address": "server.sys.exec.cmd"}], "params": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}]}, "operator": "cmdi_detector"}], "transformers": [], "on_match": ["stack_trace"]}, {"id": "rasp-934-100", "name": "Server-side request forgery exploit", "tags": {"type": "ssrf", "category": "vulnerability_trigger", "cwe": "918", "capec": "1000/225/115/664", "confidence": "0", "module": "rasp"}, "conditions": [{"parameters": {"inputs": [{"address": "server.io.net.url"}], "regex": "^(jar:)?https?:\\/\\/\\W*([0-9oq]{1,5}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}|[0-9]{1,10}|(\\[)?[:0-9a-f\\.x]{2,}(\\])?|metadata\\.google\\.internal|(?:[a-z0-9:@\\.\\-]*\\.)?(?:burpcollaborator\\.net|localtest\\.me|mail\\.ebc\\.apple\\.com|bugbounty\\.dod\\.network|.*\\.[nx]ip\\.io|oastify\\.com|oast\\.(?:pro|live|site|online|fun|me)|sslip\\.io|requestbin\\.com|requestbin\\.net|hookbin\\.com|webhook\\.site|canarytokens\\.com|interact\\.sh|ngrok\\.io|bugbounty\\.click|prbly\\.win|qualysperiscope\\.com|vii\\.one|act1on3\\.ru|ifconfig\\.pro|dnslog\\.\\w+))(:[0-9]{1,5})?(\\/[^:@]*)?$", "options": {"case_sensitive": false}}, "operator": "match_regex"}, {"parameters": {"resource": [{"address": "server.io.net.url"}], "params": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}]}, "operator": "ssrf_detector"}], "transformers": [], "on_match": ["stack_trace"]}, {"id": "rasp-942-100", "name": "SQL injection exploit", "tags": {"type": "sql_injection", "category": "vulnerability_trigger", "cwe": "89", "capec": "1000/152/248/66", "confidence": "1", "module": "rasp"}, "conditions": [{"parameters": {"resource": [{"address": "server.db.statement"}], "params": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "db_type": [{"address": "server.db.system"}]}, "operator": "sqli_detector@v2"}], "transformers": [], "on_match": ["stack_trace"]}, {"id": "sqr-000-001", "name": "SSRF: Try to access the credential manager of the main cloud services", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i)^\\W*((http|ftp)s?://)?\\W*((::f{4}:)?(169|(0x)?0*a9|0+251)\\.?(254|(0x)?0*fe|0+376)[0-9a-fx\\.:]+|metadata\\.google\\.internal|metadata\\.goog)\\W*/", "options": {"min_length": 4}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "sqr-000-002", "name": "Server-side Javascript injection: Try to detect obvious JS injection", "tags": {"type": "js_code_injection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "require\\(['\"][\\w\\.]+['\"]\\)|process\\.\\w+\\([\\w\\.]*\\)|\\.toString\\(\\)", "options": {"min_length": 4}}, "operator": "match_regex"}], "transformers": ["removeNulls"]}, {"id": "sqr-000-008", "name": "Windows: Detect attempts to exfiltrate .ini files", "tags": {"type": "command_injection", "category": "attack_attempt", "cwe": "78", "capec": "1000/152/248/88", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i)[&|]\\s*type\\s+%\\w+%\\\\+\\w+\\.ini\\s*[&|]"}, "operator": "match_regex"}], "transformers": []}, {"id": "sqr-000-009", "name": "Linux: Detect attempts to exfiltrate passwd files", "tags": {"type": "command_injection", "category": "attack_attempt", "cwe": "78", "capec": "1000/152/248/88", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i)[&|]\\s*cat\\s*\\/etc\\/[\\w\\.\\/]*passwd\\s*[&|]"}, "operator": "match_regex"}], "transformers": ["cmdLine"]}, {"id": "sqr-000-010", "name": "Windows: Detect attempts to timeout a shell", "tags": {"type": "command_injection", "category": "attack_attempt", "cwe": "78", "capec": "1000/152/248/88", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(?i)[&|]\\s*timeout\\s+/t\\s+\\d+\\s*[&|]"}, "operator": "match_regex"}], "transformers": []}, {"id": "sqr-000-011", "name": "SSRF: Try to access internal OMI service (CVE-2021-38647)", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "http(s?):\\/\\/([A-Za-z0-9\\.\\-\\_]+|\\[[A-Fa-f0-9\\:]+\\]|):5986\\/wsman", "options": {"min_length": 4}}, "operator": "match_regex"}], "transformers": []}, {"id": "sqr-000-012", "name": "SSRF: Detect SSRF attempt on internal service", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^(jar:)?(http|https):\\/\\/([0-9oq]{1,5}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}|[0-9]{1,10})(:[0-9]{1,5})?(\\/[^:@]*)?$"}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "sqr-000-013", "name": "SSRF: Detect SSRF attempts using IPv6 or octal/hexdecimal obfuscation", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^(jar:)?(http|https):\\/\\/((\\[)?[:0-9a-f\\.x]{2,}(\\])?)(:[0-9]{1,5})?(\\/[^:@]*)?$"}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "sqr-000-014", "name": "SSRF: Detect SSRF domain redirection bypass", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "grpc.server.request.message"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "(http|https):\\/\\/(?:.*\\.)?(?:burpcollaborator\\.net|localtest\\.me|mail\\.ebc\\.apple\\.com|bugbounty\\.dod\\.network|.*\\.[nx]ip\\.io|oastify\\.com|oast\\.(?:pro|live|site|online|fun|me)|sslip\\.io|requestbin\\.com|requestbin\\.net|hookbin\\.com|webhook\\.site|canarytokens\\.com|interact\\.sh|ngrok\\.io|bugbounty\\.click|prbly\\.win|qualysperiscope\\.com|vii\\.one|act1on3\\.ru|dnslog\\.\\w+)"}, "operator": "match_regex"}], "transformers": []}, {"id": "sqr-000-015", "name": "SSRF: Detect SSRF attempt using non HTTP protocol", "tags": {"type": "ssrf", "category": "attack_attempt", "cwe": "918", "capec": "1000/225/115/664", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "^(jar:)?((file|netdoc):\\/\\/[\\\\\\/]+|(dict|gopher|ldap|sftp|tftp):\\/\\/.*:[0-9]{1,5})"}, "operator": "match_regex"}], "transformers": ["lowercase"]}, {"id": "sqr-000-017", "name": "Log4shell: Attempt to exploit log4j CVE-2021-44228", "tags": {"type": "exploit_detection", "category": "attack_attempt", "cwe": "94", "capec": "1000/152/242", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.uri.raw"}, {"address": "server.request.query"}, {"address": "server.request.body"}, {"address": "server.request.path_params"}, {"address": "server.request.headers.no_cookies"}, {"address": "graphql.server.all_resolvers"}, {"address": "graphql.server.resolver"}], "regex": "\\${[^j]*j[^n]*n[^d]*d[^i]*i[^:]*:[^}]*}"}, "operator": "match_regex"}], "transformers": ["unicode_normalize"]}, {"id": "ua0-600-0xx", "name": "Joomla exploitation tool", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Joomla exploitation tool", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "JDatabaseDriverMysqli"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-10x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)^Nessus(/|([ :]+SOAP))"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-12x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^Ara<PERSON>ni\\/v"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-13x", "name": "<PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bJorgee\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-14x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\b<PERSON><PERSON><PERSON>\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-15x", "name": "<PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bmetis\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-16x", "name": "SQL power injector", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "SQLPowerInjector", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "sql power injector"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-18x", "name": "N-Stealth", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "N-Stealth", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bn-stealth\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-19x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bbrutus\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-1xx", "name": "Shellshock exploitation tool", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\(\\) \\{ :; *\\}"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-20x", "name": "Netsparker", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Netsparker", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bnetsparker\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-22x", "name": "JAASCois", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "JAASCois", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\b<PERSON><PERSON>is\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-26x", "name": "<PERSON><PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bnsauditor\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-27x", "name": "<PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)Mo<PERSON>/.* <PERSON><PERSON>/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-28x", "name": "<PERSON><PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bdirbuster\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-29x", "name": "Pangolin", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Pangolin", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bpangolin\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-2xx", "name": "Qualys", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Qualys", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bqualys\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-30x", "name": "SQLNinja", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "SQLNinja", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bsqlninja\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-31x", "name": "<PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\(Nikto/[\\d\\.]+\\)"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-33x", "name": "BlackWidow", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "BlackWidow", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bblack\\s?widow\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-34x", "name": "Grendel-Scan", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Grendel-Scan", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bgrendel-scan\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-35x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bhavij\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-36x", "name": "w3af", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "w3af", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bw3af\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-37x", "name": "Nmap", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Nmap", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "nmap (nse|scripting engine|icap-client/)"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-39x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)^'?[a-z0-9_]+\\.nasl'?$"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-3xx", "name": "Evil Scanner", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "EvilScanner", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bevilScanner\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-40x", "name": "WebFuck", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "WebFuck", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bWebFuck\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-42x", "name": "OpenVAS", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "OpenVAS", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)OpenVAS\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-43x", "name": "Spider-Pig", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Spider-Pig", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "Powered by Spider-<PERSON> by tinfoilsecurity\\.com"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-44x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "Mozilla/\\d+.\\d+ zgrab"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-45x", "name": "Zmeu", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Zmeu", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bZmEu\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-47x", "name": "GoogleSecurityScanner", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "GoogleSecurityScanner", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bGoogleSecurityScanner\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-48x", "name": "Commix", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Commix", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^commix\\/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-49x", "name": "Gobuster", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Gobuster", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^gobuster\\/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-4xx", "name": "CGIchk", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "CGIchk", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bcgichk\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-51x", "name": "FFUF", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "FFUF", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)^Fu<PERSON> Faster U Fool\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-52x", "name": "N<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "N<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)^Nuclei\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-53x", "name": "<PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bTsunamiSecurityScanner\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-54x", "name": "<PERSON><PERSON><PERSON><PERSON>", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bnimbostratus-bot\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-55x", "name": "Datadog test scanner: user-agent", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Datadog Canary Test", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "grpc.server.request.metadata", "key_path": ["dd-canary"]}], "regex": "^dd-test-scanner-log(?:$|/|\\s)"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-56x", "name": "Datadog test scanner - blocking version: user-agent", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Datadog Canary Test", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}, {"address": "grpc.server.request.metadata", "key_path": ["dd-canary"]}], "regex": "^dd-test-scanner-log-block(?:$|/|\\s)"}, "operator": "match_regex"}], "transformers": [], "on_match": ["block"]}, {"id": "ua0-600-57x", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bAlertLogic-MDR-"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-58x", "name": "wfuzz", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "wfuzz", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bwfuzz\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-59x", "name": "Detectify", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Detectify", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bdetectify\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-5xx", "name": "Blind SQL Injection Brute Forcer", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "BSQLBF", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)\\bbsqlbf\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-60x", "name": "masscan", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "masscan", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^masscan/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-61x", "name": "WPScan", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "WPScan", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^wpscan\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-62x", "name": "Aon pentesting services", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "<PERSON><PERSON>", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^Aon/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-63x", "name": "FeroxBuster", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "feroxbuster", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^feroxbuster/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-64x", "name": "ddg_win", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "ddg_win", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bddg_win\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-65x", "name": "ISS", "tags": {"type": "commercial_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "iss", "confidence": "0", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bisscyberriskcrawler/\\d\\.\\d"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-66x", "name": "BountyBot", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "bountybot", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bbountybot\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-67x", "name": "ZumBot", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "zumbot", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "\\bzumbot\\b"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-6xx", "name": "Stealthy scanner", "tags": {"type": "security_scanner", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "mozilla/4\\.0 \\(compatible(; msie (?:6\\.0; (?:win32|Windows NT 5\\.0)|4\\.0; Windows NT))?\\)", "options": {"case_sensitive": false}}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-7xx", "name": "SQLmap", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "SQLmap", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "^sqlmap/"}, "operator": "match_regex"}], "transformers": []}, {"id": "ua0-600-9xx", "name": "Skipfish", "tags": {"type": "attack_tool", "category": "attack_attempt", "cwe": "200", "capec": "1000/118/169", "tool_name": "Skipfish", "confidence": "1", "module": "waf"}, "conditions": [{"parameters": {"inputs": [{"address": "server.request.headers.no_cookies", "key_path": ["user-agent"]}], "regex": "(?i)mozilla/5\\.0 sf/"}, "operator": "match_regex"}], "transformers": []}], "processors": [{"id": "http-endpoint-fingerprint", "generator": "http_endpoint_fingerprint", "conditions": [], "parameters": {"mappings": [{"method": [{"address": "server.request.method"}], "uri_raw": [{"address": "server.request.uri.raw"}], "body": [{"address": "server.request.body"}], "query": [{"address": "server.request.query"}], "output": "_dd.appsec.fp.http.endpoint"}]}, "evaluate": true, "output": true}, {"id": "extract-content", "generator": "extract_schema", "conditions": [{"operator": "equals", "parameters": {"inputs": [{"address": "waf.context.processor", "key_path": ["extract-schema"]}], "type": "boolean", "value": true}}], "parameters": {"mappings": [{"inputs": [{"address": "server.request.body"}], "output": "_dd.appsec.s.req.body"}, {"inputs": [{"address": "server.request.cookies"}], "output": "_dd.appsec.s.req.cookies"}, {"inputs": [{"address": "server.request.query"}], "output": "_dd.appsec.s.req.query"}, {"inputs": [{"address": "server.request.path_params"}], "output": "_dd.appsec.s.req.params"}, {"inputs": [{"address": "server.response.body"}], "output": "_dd.appsec.s.res.body"}, {"inputs": [{"address": "graphql.server.all_resolvers"}], "output": "_dd.appsec.s.graphql.all_resolvers"}, {"inputs": [{"address": "graphql.server.resolver"}], "output": "_dd.appsec.s.graphql.resolver"}], "scanners": [{"tags": {"category": "payment"}}, {"tags": {"category": "pii"}}]}, "evaluate": false, "output": true}, {"id": "extract-headers", "generator": "extract_schema", "conditions": [{"operator": "equals", "parameters": {"inputs": [{"address": "waf.context.processor", "key_path": ["extract-schema"]}], "type": "boolean", "value": true}}], "parameters": {"mappings": [{"inputs": [{"address": "server.request.headers.no_cookies"}], "output": "_dd.appsec.s.req.headers"}, {"inputs": [{"address": "server.response.headers.no_cookies"}], "output": "_dd.appsec.s.res.headers"}], "scanners": [{"tags": {"category": "credentials"}}, {"tags": {"category": "pii"}}]}, "evaluate": false, "output": true}, {"id": "http-header-fingerprint", "generator": "http_header_fingerprint", "conditions": [], "parameters": {"mappings": [{"headers": [{"address": "server.request.headers.no_cookies"}], "output": "_dd.appsec.fp.http.header"}]}, "evaluate": true, "output": true}, {"id": "http-network-fingerprint", "generator": "http_network_fingerprint", "conditions": [], "parameters": {"mappings": [{"headers": [{"address": "server.request.headers.no_cookies"}], "output": "_dd.appsec.fp.http.network"}]}, "evaluate": true, "output": true}, {"id": "session-fingerprint", "generator": "session_fingerprint", "conditions": [], "parameters": {"mappings": [{"cookies": [{"address": "server.request.cookies"}], "session_id": [{"address": "usr.session_id"}], "user_id": [{"address": "usr.id"}], "output": "_dd.appsec.fp.session"}]}, "evaluate": true, "output": true}], "scanners": [{"id": "406f8606-52c4-4663-8db9-df70f9e8766c", "name": "ZIP Code", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:zip|postal)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "^[0-9]{5}(?:-[0-9]{4})?$", "options": {"case_sensitive": true, "min_length": 5}}}, "tags": {"type": "zipcode", "category": "address"}}, {"id": "JU1sRk3mSzqSUJn6GrVn7g", "name": "American Express Card Scanner (4+4+4+3 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b3[47]\\d{2}(?:(?:\\s\\d{4}\\s\\d{4}\\s\\d{3})|(?:\\,\\d{4}\\,\\d{4}\\,\\d{3})|(?:-\\d{4}-\\d{4}-\\d{3})|(?:\\.\\d{4}\\.\\d{4}\\.\\d{3}))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "amex", "category": "payment"}}, {"id": "edmH513UTQWcRiQ9UnzHlw-mod", "name": "American Express Card Scanner (4+6|5+5|6 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b3[47]\\d{2}(?:(?:\\s\\d{5,6}\\s\\d{5,6})|(?:\\.\\d{5,6}\\.\\d{5,6})|(?:-\\d{5,6}-\\d{5,6})|(?:,\\d{5,6},\\d{5,6}))\\b", "options": {"case_sensitive": false, "min_length": 17}}}, "tags": {"type": "card", "card_type": "amex", "category": "payment"}}, {"id": "e6K4h_7qTLaMiAbaNXoSZA", "name": "American Express Card Scanner (8+7 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b3[47]\\d{6}(?:(?:\\s\\d{7})|(?:\\,\\d{7})|(?:-\\d{7})|(?:\\.\\d{7}))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "amex", "category": "payment"}}, {"id": "K2rZflWzRhGM9HiTc6whyQ", "name": "American Express Card Scanner (1x15 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b3[47]\\d{13}\\b", "options": {"case_sensitive": false, "min_length": 15}}}, "tags": {"type": "card", "card_type": "amex", "category": "payment"}}, {"id": "9d7756e343cefa22a5c098e1092590f806eb5446", "name": "Basic Authentication Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\bauthorization\\b", "options": {"case_sensitive": false, "min_length": 13}}}, "value": {"operator": "match_regex", "parameters": {"regex": "^basic\\s+[A-Za-z0-9+/=]+", "options": {"case_sensitive": false, "min_length": 7}}}, "tags": {"type": "basic_auth", "category": "credentials"}}, {"id": "mZy8XjZLReC9smpERXWnnw", "name": "Bearer Authentication Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\bauthorization\\b", "options": {"case_sensitive": false, "min_length": 13}}}, "value": {"operator": "match_regex", "parameters": {"regex": "^bearer\\s+[-a-z0-9._~+/]{4,}", "options": {"case_sensitive": false, "min_length": 11}}}, "tags": {"type": "bearer_token", "category": "credentials"}}, {"id": "450239afc250a19799b6c03dc0e16fd6a4b2a1af", "name": "Canadian Social Insurance Number Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:social[\\s_]?(?:insurance(?:\\s+number)?)?|SIN|Canadian[\\s_]?(?:social[\\s_]?(?:insurance)?|insurance[\\s_]?number)?)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b\\d{3}-\\d{3}-\\d{3}\\b", "options": {"case_sensitive": false, "min_length": 11}}}, "tags": {"type": "canadian_sin", "category": "pii"}}, {"id": "87a879ff33693b46c8a614d8211f5a2c289beca0", "name": "Digest Authentication Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\bauthorization\\b", "options": {"case_sensitive": false, "min_length": 13}}}, "value": {"operator": "match_regex", "parameters": {"regex": "^digest\\s+", "options": {"case_sensitive": false, "min_length": 7}}}, "tags": {"type": "digest_auth", "category": "credentials"}}, {"id": "qWumeP1GQUa_E4ffAnT-Yg", "name": "American Express Card Scanner (1x14 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "(?:30[0-59]\\d|3[689]\\d{2})(?:\\d{10})", "options": {"case_sensitive": false, "min_length": 14}}}, "tags": {"type": "card", "card_type": "diners", "category": "payment"}}, {"id": "NlTWWM5LS6W0GSqBLuvtRw", "name": "Diners <PERSON> Scanner (4+4+4+2 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:30[0-59]\\d|3[689]\\d{2})(?:(?:\\s\\d{4}\\s\\d{4}\\s\\d{2})|(?:\\,\\d{4}\\,\\d{4}\\,\\d{2})|(?:-\\d{4}-\\d{4}-\\d{2})|(?:\\.\\d{4}\\.\\d{4}\\.\\d{2}))\\b", "options": {"case_sensitive": false, "min_length": 17}}}, "tags": {"type": "card", "card_type": "diners", "category": "payment"}}, {"id": "Xr5VdbQSTXitYGGiTfxBpw", "name": "Diners Card Scanner (4+6+4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:30[0-59]\\d|3[689]\\d{2})(?:(?:\\s\\d{6}\\s\\d{4})|(?:\\.\\d{6}\\.\\d{4})|(?:-\\d{6}-\\d{4})|(?:,\\d{6},\\d{4}))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "diners", "category": "payment"}}, {"id": "gAbunN_WQNytxu54DjcbAA-mod", "name": "Diners Card Scanner (8+6 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:30[0-59]\\d{5}|3[689]\\d{6})\\s?(?:(?:\\s\\d{6})|(?:\\,\\d{6})|(?:-\\d{6})|(?:\\.\\d{6}))\\b", "options": {"case_sensitive": false, "min_length": 14}}}, "tags": {"type": "card", "card_type": "diners", "category": "payment"}}, {"id": "9cs4qCfEQBeX17U7AepOvQ", "name": "Master<PERSON><PERSON> (2x8 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:6221(?:2[6-9]|[3-9][0-9])\\d{2}(?:,\\d{8}|\\s\\d{8}|-\\d{8}|\\.\\d{8})|6229(?:[01][0-9]|2[0-5])\\d{2}(?:,\\d{8}|\\s\\d{8}|-\\d{8}|\\.\\d{8})|(?:6011|65\\d{2}|64[4-9]\\d|622[2-8])\\d{4}(?:,\\d{8}|\\s\\d{8}|-\\d{8}|\\.\\d{8}))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "discover", "category": "payment"}}, {"id": "YBIDWJIvQWW_TFOyU0CGJg", "name": "Discover Card Scanner (4x4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:(?:(?:6221(?:2[6-9]|[3-9][0-9])\\d{2}(?:,\\d{4}){2})|(?:6221\\s(?:2[6-9]|[3-9][0-9])\\d{2}(?:\\s\\d{4}){2})|(?:6221\\.(?:2[6-9]|[3-9][0-9])\\d{2}(?:\\.\\d{4}){2})|(?:6221-(?:2[6-9]|[3-9][0-9])\\d{2}(?:-\\d{4}){2}))|(?:(?:6229(?:[01][0-9]|2[0-5])\\d{2}(?:,\\d{4}){2})|(?:6229\\s(?:[01][0-9]|2[0-5])\\d{2}(?:\\s\\d{4}){2})|(?:6229\\.(?:[01][0-9]|2[0-5])\\d{2}(?:\\.\\d{4}){2})|(?:6229-(?:[01][0-9]|2[0-5])\\d{2}(?:-\\d{4}){2}))|(?:(?:6011|65\\d{2}|64[4-9]\\d|622[2-8])(?:(?:\\s\\d{4}){3}|(?:\\.\\d{4}){3}|(?:-\\d{4}){3}|(?:,\\d{4}){3})))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "discover", "category": "payment"}}, {"id": "12cpbjtVTMaMutFhh9sojQ", "name": "Discover Card Scanner (1x16 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:6221(?:2[6-9]|[3-9][0-9])\\d{10}|6229(?:[01][0-9]|2[0-5])\\d{10}|(?:6011|65\\d{2}|64[4-9]\\d|622[2-8])\\d{12})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "discover", "category": "payment"}}, {"id": "PuXiVTCkTHOtj0Yad1ppsw", "name": "Standard E-mail Address", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:(?:e[-\\s]?)?mail|address|sender|\\bto\\b|from|recipient)\\b", "options": {"case_sensitive": false, "min_length": 2}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b[\\w!#$%&'*+/=?`{|}~^-]+(?:\\.[\\w!#$%&'*+/=?`{|}~^-]+)*(%40|@)(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,6}\\b", "options": {"case_sensitive": false, "min_length": 5}}}, "tags": {"type": "email", "category": "pii"}}, {"id": "8VS2RKxzR8a_95L5fuwaXQ", "name": "IBAN", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:iban|account|sender|receiver)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:NO\\d{2}(?:[ \\-]?\\d{4}){2}[ \\-]?\\d{3}|BE\\d{2}(?:[ \\-]?\\d{4}){3}|(?:DK|FO|FI|GL|SD)\\d{2}(?:[ \\-]?\\d{4}){3}[ \\-]?\\d{2}|NL\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?\\d{4}){2}[ \\-]?\\d{2}|MK\\d{2}[ \\-]?\\d{3}[A-Z0-9](?:[ \\-]?[A-Z0-9]{4}){2}[ \\-]?[A-Z0-9]\\d{2}|SI\\d{17}|(?:AT|BA|EE|LT|XK)\\d{18}|(?:LU|KZ|EE|LT)\\d{5}[A-Z0-9]{13}|LV\\d{2}[A-Z]{4}[A-Z0-9]{13}|(?:LI|CH)\\d{2}[ \\-]?\\d{4}[ \\-]?\\d[A-Z0-9]{3}(?:[ \\-]?[A-Z0-9]{4}){2}[ \\-]?[A-Z0-9]|HR\\d{2}(?:[ \\-]?\\d{4}){4}[ \\-]?\\d|GE\\d{2}[ \\-]?[A-Z0-9]{2}\\d{2}\\d{14}|VA\\d{20}|BG\\d{2}[A-Z]{4}\\d{6}[A-Z0-9]{8}|BH\\d{2}[A-Z]{4}[A-Z0-9]{14}|GB\\d{2}[A-Z]{4}(?:[ \\-]?\\d{4}){3}[ \\-]?\\d{2}|IE\\d{2}[ \\-]?[A-Z0-9]{4}(?:[ \\-]?\\d{4}){3}[ \\-]?\\d{2}|(?:CR|DE|ME|RS)\\d{2}(?:[ \\-]?\\d{4}){4}[ \\-]?\\d{2}|(?:AE|TL|IL)\\d{2}(?:[ \\-]?\\d{4}){4}[ \\-]?\\d{3}|GI\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?[A-Z0-9]{4}){3}[ \\-]?[A-Z0-9]{3}|IQ\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?\\d{4}){3}[ \\-]?\\d{3}|MD\\d{2}(?:[ \\-]?[A-Z0-9]{4}){5}|SA\\d{2}[ \\-]?\\d{2}[A-Z0-9]{2}(?:[ \\-]?[A-Z0-9]{4}){4}|RO\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?[A-Z0-9]{4}){4}|(?:PK|VG)\\d{2}[ \\-]?[A-Z0-9]{4}(?:[ \\-]?\\d{4}){4}|AD\\d{2}(?:[ \\-]?\\d{4}){2}(?:[ \\-]?[A-Z0-9]{4}){3}|(?:CZ|SK|ES|SE|TN)\\d{2}(?:[ \\-]?\\d{4}){5}|(?:LY|PT|ST)\\d{2}(?:[ \\-]?\\d{4}){5}[ \\-]?\\d|TR\\d{2}[ \\-]?\\d{4}[ \\-]?\\d[A-Z0-9]{3}(?:[ \\-]?[A-Z0-9]{4}){3}[ \\-]?[A-Z0-9]{2}|IS\\d{2}(?:[ \\-]?\\d{4}){5}[ \\-]?\\d{2}|(?:IT|SM)\\d{2}[ \\-]?[A-Z]\\d{3}[ \\-]?\\d{4}[ \\-]?\\d{3}[A-Z0-9](?:[ \\-]?[A-Z0-9]{4}){2}[ \\-]?[A-Z0-9]{3}|GR\\d{2}[ \\-]?\\d{4}[ \\-]?\\d{3}[A-Z0-9](?:[ \\-]?[A-Z0-9]{4}){3}[A-Z0-9]{3}|(?:FR|MC)\\d{2}(?:[ \\-]?\\d{4}){2}[ \\-]?\\d{2}[A-Z0-9]{2}(?:[ \\-]?[A-Z0-9]{4}){2}[ \\-]?[A-Z0-9]\\d{2}|MR\\d{2}(?:[ \\-]?\\d{4}){5}[ \\-]?\\d{3}|(?:SV|DO)\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?\\d{4}){5}|BY\\d{2}[ \\-]?[A-Z]{4}[ \\-]?\\d{4}(?:[ \\-]?[A-Z0-9]{4}){4}|GT\\d{2}(?:[ \\-]?[A-Z0-9]{4}){6}|AZ\\d{2}[ \\-]?[A-Z0-9]{4}(?:[ \\-]?\\d{5}){4}|LB\\d{2}[ \\-]?\\d{4}(?:[ \\-]?[A-Z0-9]{5}){4}|(?:AL|CY)\\d{2}(?:[ \\-]?\\d{4}){2}(?:[ \\-]?[A-Z0-9]{4}){4}|(?:HU|PL)\\d{2}(?:[ \\-]?\\d{4}){6}|QA\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?[A-Z0-9]{4}){5}[ \\-]?[A-Z0-9]|PS\\d{2}[ \\-]?[A-Z0-9]{4}(?:[ \\-]?\\d{4}){5}[ \\-]?\\d|UA\\d{2}[ \\-]?\\d{4}[ \\-]?\\d{2}[A-Z0-9]{2}(?:[ \\-]?[A-Z0-9]{4}){4}[ \\-]?[A-Z0-9]|BR\\d{2}(?:[ \\-]?\\d{4}){5}[ \\-]?\\d{3}[A-Z0-9][ \\-]?[A-Z0-9]|EG\\d{2}(?:[ \\-]?\\d{4}){6}\\d|MU\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?\\d{4}){4}\\d{3}[A-Z][ \\-]?[A-Z]{2}|(?:KW|JO)\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?[A-Z0-9]{4}){5}[ \\-]?[A-Z0-9]{2}|MT\\d{2}[ \\-]?[A-Z]{4}[ \\-]?\\d{4}[ \\-]?\\d[A-Z0-9]{3}(?:[ \\-]?[A-Z0-9]{3}){4}[ \\-]?[A-Z0-9]{3}|SC\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?\\d{4}){5}[ \\-]?[A-Z]{3}|LC\\d{2}[ \\-]?[A-Z]{4}(?:[ \\-]?[A-Z0-9]{4}){6})\\b", "options": {"case_sensitive": false, "min_length": 15}}}, "tags": {"type": "iban", "category": "payment"}}, {"id": "h6WJcecQTwqvN9KeEtwDvg", "name": "JCB Card Scanner (1x16 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b35(?:2[89]|[3-9][0-9])(?:\\d{12})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "jcb", "category": "payment"}}, {"id": "gcEaMu_VSJ2-bGCEkgyC0w", "name": "JCB Card Scanner (2x8 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b35(?:2[89]|[3-9][0-9])\\d{4}(?:(?:,\\d{8})|(?:-\\d{8})|(?:\\s\\d{8})|(?:\\.\\d{8}))\\b", "options": {"case_sensitive": false, "min_length": 17}}}, "tags": {"type": "card", "card_type": "jcb", "category": "payment"}}, {"id": "imTliuhXT5GAeRNhqChXQQ", "name": "JCB Card Scanner (4x4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b35(?:2[89]|[3-9][0-9])(?:(?:\\s\\d{4}){3}|(?:\\.\\d{4}){3}|(?:-\\d{4}){3}|(?:,\\d{4}){3})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "jcb", "category": "payment"}}, {"id": "9osY3xc9Q7ONAV0zw9Uz4A", "name": "JSON Web Token", "value": {"operator": "match_regex", "parameters": {"regex": "\\bey[I-L][\\w=-]+\\.ey[I-L][\\w=-]+(\\.[\\w.+\\/=-]+)?\\b", "options": {"case_sensitive": false, "min_length": 20}}}, "tags": {"type": "json_web_token", "category": "credentials"}}, {"id": "d1Q9D3YMRxuVKf6CZInJPw", "name": "Maestro Card Scanner (1x16 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:5[06-9]\\d{2}|6\\d{3})(?:\\d{12})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "maestro", "category": "payment"}}, {"id": "M3YIQKKjRVmoeQuM3pjzrw", "name": "Maestro Card Scanner (2x8 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:5[06-9]\\d{6}|6\\d{7})(?:\\s\\d{8}|\\.\\d{8}|-\\d{8}|,\\d{8})\\b", "options": {"case_sensitive": false, "min_length": 17}}}, "tags": {"type": "card", "card_type": "maestro", "category": "payment"}}, {"id": "hRxiQBlSSVKcjh5U7LZYLA", "name": "Maestro Card Scanner (4x4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:5[06-9]\\d{2}|6\\d{3})(?:(?:\\s\\d{4}){3}|(?:\\.\\d{4}){3}|(?:-\\d{4}){3}|(?:,\\d{4}){3})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "maestro", "category": "payment"}}, {"id": "NwhIYNS4STqZys37WlaIKA", "name": "Master<PERSON><PERSON> (2x8 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:(?:5[1-5]\\d{2})|(?:222[1-9])|(?:22[3-9]\\d)|(?:2[3-6]\\d{2})|(?:27[0-1]\\d)|(?:2720))(?:(?:\\d{4}(?:(?:,\\d{8})|(?:-\\d{8})|(?:\\s\\d{8})|(?:\\.\\d{8}))))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "mastercard", "category": "payment"}}, {"id": "axxJkyjhRTOuhjwlsA35Vw", "name": "Master<PERSON><PERSON> (4x4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:(?:5[1-5]\\d{2})|(?:222[1-9])|(?:22[3-9]\\d)|(?:2[3-6]\\d{2})|(?:27[0-1]\\d)|(?:2720))(?:(?:\\s\\d{4}){3}|(?:\\.\\d{4}){3}|(?:-\\d{4}){3}|(?:,\\d{4}){3})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "mastercard", "category": "payment"}}, {"id": "76EhmoK3TPqJcpM-fK0pLw", "name": "Master<PERSON>ard Scanner (1x16 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b(?:(?:5[1-5]\\d{2})|(?:222[1-9])|(?:22[3-9]\\d)|(?:2[3-6]\\d{2})|(?:27[0-1]\\d)|(?:2720))(?:\\d{12})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "mastercard", "category": "payment"}}, {"id": "18b608bd7a764bff5b2344c0", "name": "Phone number", "key": {"operator": "match_regex", "parameters": {"regex": "\\bphone|number|mobile\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "^(?:\\(\\+\\d{1,3}\\)|\\+\\d{1,3}|00\\d{1,3})?[-\\s\\.]?(?:\\(\\d{3}\\)[-\\s\\.]?)?(?:\\d[-\\s\\.]?){6,10}$", "options": {"case_sensitive": false, "min_length": 6}}}, "tags": {"type": "phone", "category": "pii"}}, {"id": "de0899e0cbaaa812bb624cf04c912071012f616d-mod", "name": "UK National Insurance Number Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "^nin$|\\binsurance\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b[A-Z]{2}[\\s-]?\\d{6}[\\s-]?[A-Z]?\\b", "options": {"case_sensitive": false, "min_length": 8}}}, "tags": {"type": "uk_nin", "category": "pii"}}, {"id": "d962f7ddb3f55041e39195a60ff79d4814a7c331", "name": "US Passport Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\bpassport\\b", "options": {"case_sensitive": false, "min_length": 8}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b[0-9A-Z]{9}\\b|\\b[0-9]{6}[A-Z][0-9]{2}\\b", "options": {"case_sensitive": false, "min_length": 8}}}, "tags": {"type": "passport_number", "category": "pii"}}, {"id": "7771fc3b-b205-4b93-bcef-28608c5c1b54", "name": "United States Social Security Number Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:SSN|(?:(?:social)?[\\s_]?(?:security)?[\\s_]?(?:number)?)?)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b\\d{3}[-\\s\\.]{1}\\d{2}[-\\s\\.]{1}\\d{4}\\b", "options": {"case_sensitive": false, "min_length": 11}}}, "tags": {"type": "us_ssn", "category": "pii"}}, {"id": "ac6d683cbac77f6e399a14990793dd8fd0fca333", "name": "US Vehicle Identification Number Scanner", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:vehicle[_\\s-]*identification[_\\s-]*number|vin)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b[A-HJ-NPR-Z0-9]{17}\\b", "options": {"case_sensitive": false, "min_length": 17}}}, "tags": {"type": "vin", "category": "pii"}}, {"id": "wJIgOygRQhKkR69b_9XbRQ", "name": "Visa Card Scanner (2x8 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b4\\d{3}(?:(?:\\d{4}(?:(?:,\\d{8})|(?:-\\d{8})|(?:\\s\\d{8})|(?:\\.\\d{8}))))\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "visa", "category": "payment"}}, {"id": "0o71SJxXQNK7Q6gMbBesFQ", "name": "Visa Card Scanner (4x4 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "\\b4\\d{3}(?:(?:,\\d{4}){3}|(?:\\s\\d{4}){3}|(?:\\.\\d{4}){3}|(?:-\\d{4}){3})\\b", "options": {"case_sensitive": false, "min_length": 16}}}, "tags": {"type": "card", "card_type": "visa", "category": "payment"}}, {"id": "QrHD6AfgQm6z-j0wStxTvA", "name": "Visa Card Scanner (1x15 & 1x16 & 1x19 digits)", "key": {"operator": "match_regex", "parameters": {"regex": "\\b(?:card|cc|credit|debit|payment|amex|visa|mastercard|maestro|discover|jcb|diner)\\b", "options": {"case_sensitive": false, "min_length": 3}}}, "value": {"operator": "match_regex", "parameters": {"regex": "4[0-9]{12}(?:[0-9]{3})?", "options": {"case_sensitive": false, "min_length": 13}}}, "tags": {"type": "card", "card_type": "visa", "category": "payment"}}]}