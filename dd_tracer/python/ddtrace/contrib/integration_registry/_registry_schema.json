{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Datadog Python Integration Registry Schema", "description": "Schema matching the current fields generated by scripts/integration_registry/update_and_format_registry.py.", "type": "object", "properties": {"integrations": {"description": "List of all Datadog integration definitions.", "type": "array", "minItems": 1, "items": {"$ref": "#/definitions/integration"}}}, "required": ["integrations"], "additionalProperties": false, "definitions": {"testedVersionInfo": {"type": "object", "description": "Min, max, and list of tested versions for a specific dependency.", "properties": {"min": {"description": "Minimum explicit version found in test lockfiles.", "type": "string"}, "max": {"description": "Maximum explicit version found in test lockfiles.", "type": "string"}}, "required": ["min", "max"], "additionalProperties": false}, "integration": {"type": "object", "description": "Schema for a single integration definition.", "properties": {"integration_name": {"description": "Canonical integration name used within ddtrace (lowercase, snake_case).", "type": "string", "pattern": "^[a-z0-9_]+$"}, "is_external_package": {"description": "True if this instruments an external PyPI package, False for stdlib/internal/testing.", "type": "boolean"}, "is_tested": {"description": "False if the integration is not tested, true if it is tested.", "type": "boolean"}, "dependency_names": {"description": "Optional: List of primary PyPI package names instrumented (present only if is_external_package=true and dependencies were identified).", "type": "array", "items": {"type": "string"}, "minItems": 1}, "tested_versions_by_dependency": {"description": "Optional: Maps dependency names (from dependency_names list) to their tested version info. Present only if is_external_package=true and is_tested=true.", "type": "object", "additionalProperties": {"$ref": "#/definitions/testedVersionInfo"}}}, "required": ["integration_name", "is_external_package", "is_tested"], "additionalProperties": false}}}