FRAMEWORK = "pytest"
KIND = "test"


# XFail Reason
XFAIL_REASON = "pytest.xfail.reason"

ITR_MIN_SUPPORTED_VERSION = (7, 2, 0)
RETRIES_MIN_SUPPORTED_VERSION = (7, 0, 0)
EFD_MIN_SUPPORTED_VERSION = RET<PERSON>ES_MIN_SUPPORTED_VERSION
ATR_MIN_SUPPORTED_VERSION = RETRIES_MIN_SUPPORTED_VERSION
ATTEMPT_TO_FIX_MIN_SUPPORTED_VERSION = RETRIES_MIN_SUPPORTED_VERSION

USER_PROPERTY_QUARANTINED = ("dd_quarantined", True)
