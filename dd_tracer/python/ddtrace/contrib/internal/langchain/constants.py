text_embedding_models = (
    "HuggingFaceEmbeddings",
    "CohereEmbeddings",
    "ElasticsearchEmbeddings",
    "JinaEmbeddings",
    "LlamaCppEmbeddings",
    "HuggingFaceHubEmbeddings",
    "ModelScopeEmbeddings",
    "TensorflowHubEmbeddings",
    "SagemakerEndpointEmbeddings",
    "HuggingFaceInstructEmbeddings",
    "MosaicMLInstructorEmbeddings",
    "SelfHostedEmbeddings",
    "SelfHostedHuggingFaceEmbeddings",
    "SelfHostedHuggingFaceInstructEmbeddings",
    "FakeEmbeddings",
    "AlephAlphaAsymmetricSemanticEmbedding",
    "AlephAlphaSymmetricSemanticEmbedding",
    "SentenceTransformerEmbeddings",
    "GooglePalmEmbeddings",
    "MiniMaxEmbeddings",
    "VertexAIEmbeddings",
    "BedrockEmbeddings",
    "DeepInfraEmbeddings",
    "DashScopeEmbeddings",
    "EmbaasEmbeddings",
)

vectorstore_classes = (
    "AzureSearch",
    "Redis",
    "ElasticVectorSearch",
    "FAISS",
    "VectorStore",
    "Pinecone",
    "Weaviate",
    "Qdrant",
    "Milvus",
    "Zilliz",
    "SingleStoreDB",
    "Chroma",
    "OpenSearchVectorSearch",
    "AtlasDB",
    "DeepLake",
    "Annoy",
    "MongoDBAtlasVectorSearch",
    "MyScale",
    "SKLearnVectorStore",
    "SupabaseVectorStore",
    "AnalyticDB",
    "Vectara",
    "Tair",
    "LanceDB",
    "DocArrayHnswSearch",
    "DocArrayInMemorySearch",
    "Typesense",
    "Hologres",
    "Clickhouse",
    "Tigris",
    "MatchingEngine",
    "AwaDB",
)

API_KEY = "langchain.request.api_key"
