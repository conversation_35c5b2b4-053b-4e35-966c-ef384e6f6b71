from typing import Dict

class TagsetDecodeError(ValueError): ...
class TagsetEncodeError(ValueError): ...

class TagsetMaxSizeEncodeError(TagsetEncodeError):
    values: Dict[str, str]
    max_size: int
    current_results: str
    def __init__(self, values: Dict[str, str], max_size: int, current_results: str): ...

class TagsetMaxSizeDecodeError(TagsetDecodeError):
    value: Dict[str, str]
    max_size: int
    def __init__(self, value: Dict[str, str], max_size: int): ...

def decode_tagset_string(tagset: str) -> Dict[str, str]: ...
def encode_tagset_values(values: Dict[str, str], max_size: int = 512) -> str: ...
