[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-api"
dynamic = ["version"]
description = ""
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = { text = "Elastic-2.0" }
requires-python = ">=3.11"
readme = "README.md"
dependencies = [
    "sse-starlette>=2.1.0,<2.2.0",
    "starlette>=0.38.6",
    "watchfiles>=0.13",
    "langgraph>=0.4.0",
    "langgraph-checkpoint>=2.0.23",
    "orjson>=3.9.7",
    "uvicorn>=0.26.0",
    "langsmith>=0.3.45",
    "httpx>=0.25.0",
    "langchain-core>=0.3.64",
    "tenacity>=8.0.0",
    "jsonschema-rs>=0.20.0,<0.30",
    "structlog>=24.1.0,<26",
    "pyjwt>=2.9.0",
    "cryptography>=42.0.0,<45.0",
    "langgraph-sdk>=0.2.0",
    "cloudpickle>=3.0.0",
    "langgraph-runtime-inmem>=0.8.0,<0.9.0",
    "truststore>=0.1",
]

[project.scripts]
langgraph-verify-graphs = "langgraph_api.graph:verify_graphs"

[project.packages]
find = { where = ["."] }

[dependency-groups]
dev = [
    "ruff>=0.11.10",
    "codespell>=2.2.0",
    "pytest>=7.4.4",
    "anyio>=4.4.0",
    "pytest-watcher>=0.4.2",
    "langgraph-cli>=0.1.72",
    "pytest-repeat>=0.9.3",
    "pytest-retry>=1.6.3",
    "pytest-httpserver>=1.1.3",
    "fastapi>=0.115.8",
    "pycryptodome>=3.22.0",
    "blockbuster>=1.5.24",
    "passlib>=1.7.4",
    "langgraph-checkpoint-sqlite>=2.0.10",
]


[tool.uv]
default-groups = ["dev"]

[tool.uv.sources]
langgraph-runtime-inmem = { path = "../runtime_inmem", editable = true }
[tool.hatch.build]
exclude = ["tests/"]

[tool.hatch.build.targets.sdist]
exclude = [
    "tests/",
    "docs/",
    ".github/",
    "dist/",
    "dist*/**",
    "examples.py",
    "examples/",
    ".editorconfig",
    "db/",
    "evals/",
    ".langgraph_api",
    "*.ipynb",
    ".python-version",
    ".editorconfig",
    ".venv*/**",
    "pytest.ini",
    "node_modules/",
    ".dockerignore",
]


[tool.hatch.build.targets.wheel]
packages = ["langgraph_api", "langgraph_runtime", "langgraph_license"]

[tool.hatch.build.targets.wheel.force-include]
"openapi.json" = "openapi.json"
"logging.json" = "logging.json"
"LICENSE" = "LICENSE"

[tool.hatch.version]
path = "langgraph_api/__init__.py"

[tool.pytest.ini_options]
# --strict-markers will raise errors on unknown marks.
# https://docs.pytest.org/en/7.1.x/how-to/mark.html#raising-errors-on-unknown-marks
#
# https://docs.pytest.org/en/7.1.x/reference/reference.html
# --strict-config       any warnings encountered while parsing the `pytest`
#                       section of the configuration file raise errors.
addopts = "--strict-markers --strict-config --durations=5 -vv"

[tool.ruff]
lint.select = ["F821", "E", "F", "UP", "TRY400", "B", "SIM", "I"]
lint.ignore = ["E501", "B008"]
extend-exclude = [".venv", "dist", "build", "__pycache__", "venv", "tests"]
target-version = "py311"

[tool.ty.rules]
possibly-unbound-attribute = "error"
possibly-unbound-implicit-call = "error"
possibly-unresolved-reference = "error"
unresolved-global = "error"
unsupported-base = "error"

[tool.pytest-watcher]
now = true
delay = 3
patterns = ["*.py"]
