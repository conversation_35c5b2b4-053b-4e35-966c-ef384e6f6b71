# When editing this file, also update docs pages:
# https://github.com/langchain-ai/langgraph/blob/main/docs/docs/cloud/deployment/setup.md
# https://github.com/langchain-ai/langgraph/blob/main/docs/docs/cloud/deployment/setup_pyproject.md
langgraph>=0.4.0
langgraph-sdk>=0.2.0
langgraph-checkpoint>=2.0.23
langchain-core>=0.3.64
langsmith>=0.3.45
orjson>=3.9.7,<3.10.17
httpx>=0.25.0
tenacity>=8.0.0
uvicorn>=0.26.0
sse-starlette>=2.1.0,<2.2.0
uvloop>=0.18.0
httptools>=0.5.0
jsonschema-rs>=0.20.0
structlog>=24.1.0
cloudpickle>=3.0.0
truststore>=0.1
pyjwt>=2.8.0
cryptography>=41.0.0
# Postgres constraints
psycopg>=3.2.8
psycopg-pool>=3.2.2
psycopg-binary>=3.2.0
langgraph-checkpoint-postgres>=2.0.17
coredis>=4.21.0,<5.0
croniter>=1.0.1
pyzmq>=26.2.0
